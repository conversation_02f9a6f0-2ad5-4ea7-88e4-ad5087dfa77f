# MVP Issue Priority & Roadmap

Below is the prioritized order of issues to complete the MVP, grouped by functional area. Each issue includes its order, title, issue number, a short description, and a direct GitHub link.

---

## 0. Frontend Enhancement & Implementation (COMPLETED)

*Recently completed frontend enhancements to support the MVP workflow*

- [x] **Enhanced Publisher Campaign List Page**
  - Added comprehensive search and filtering functionality
  - Implemented grid/list view toggle with improved UI
  - Added status badges for campaign and application status
  - Integrated with backend API structure (ready for implementation)
  - Added responsive design and loading/error states
  - Implemented action buttons for viewing details and applying to campaigns

- [x] **Enhanced Publisher Campaign Detail Page**
  - Complete redesign with comprehensive campaign information display
  - Added advertiser information section
  - Implemented application status tracking and feedback
  - Added deadline validation and application restrictions
  - Enhanced UI with proper status indicators and action buttons
  - Integrated with backend API structure (ready for implementation)

- [x] **Created Missing GitHub Issues for Frontend Features**
  - SP Team Application Review Dashboard [#NEW]
  - Advertiser Application Review Dashboard [#NEW]
  - Publisher Video Submission Interface [#NEW]
  - Video Review Interface for SP Team and Advertisers [#NEW]
  - Enhanced Campaign Management Interface for SP Team [#NEW]
  - Real-time Notification System [#NEW]

---

## 1. Platform Initialization & Architecture

*Breakdown from: [Backend: Finalize System Architecture & Database Design #25](https://github.com/tan-interspace/pay-per-post/issues/25)*

- [x] **[Sub] Backend: Finalize Overall System Architecture & Data Flow** [#37](https://github.com/tan-interspace/pay-per-post/issues/37)
  - Analyze, define services, boundaries, data flow, and create the overall architecture diagram.
- [x] **[Sub] Backend: Design Database Schema for Users Entity** [#38](https://github.com/tan-interspace/pay-per-post/issues/38)
  - Design detailed schema for Users, define fields, relationships, and sample migration.
- [x] **[Sub] Backend: Design Database Schema for Roles Entity** [#39](https://github.com/tan-interspace/pay-per-post/issues/39)
  - Design detailed schema for Roles, define fields, relationships, and sample migration.
- [x] **[Sub] Backend: Design Database Schema for Campaigns Entity** [#40](https://github.com/tan-interspace/pay-per-post/issues/40)
  - Design detailed schema for Campaigns, define fields, relationships, and sample migration.
- [x] **[Sub] Backend: Design Database Schema for Applications Entity** [#41](https://github.com/tan-interspace/pay-per-post/issues/41)
  - Design detailed schema for Applications, define fields, relationships, and sample migration.
- [x] **[Sub] Backend: Design Database Schema for VideoSubmissions Entity** [#42](https://github.com/tan-interspace/pay-per-post/issues/42)
  - Design detailed schema for VideoSubmissions, define fields, relationships, and sample migration.
- [x] **[Sub] Backend: Initialize NestJS Backend Repository** [#43](https://github.com/tan-interspace/pay-per-post/issues/43)
  - Scaffold a new NestJS backend project, set up folder structure, push to GitHub.
- [x] **[Sub] Frontend: Initialize Next.js Frontend Repository** [#44](https://github.com/tan-interspace/pay-per-post/issues/44)
  - Scaffold a new Next.js frontend project, set up folder structure, push to GitHub. ([PR #179](https://github.com/tan-interspace/pay-per-post/pull/179))
- [x] **[Sub] Backend: Configure ORM and Database Connection** [#45](https://github.com/tan-interspace/pay-per-post/issues/45)
  - Configure ORM (TypeORM/Prisma), set up DB connection, document env variables.
- [x] **[Sub] Backend: Add Initial Project Documentation** [#46](https://github.com/tan-interspace/pay-per-post/issues/46)
  - Create README with setup instructions for both frontend and backend.
- [x] **Set Up Docker-based Local Development Environment for Backend** [#157](https://github.com/tan-interspace/pay-per-post/issues/157)
  - Create Dockerfile and docker-compose.yml for backend.
- [x] **Backend: Design and Implement User Entity/Model (NestJS)** [#1](https://github.com/tan-interspace/pay-per-post/issues/1)
  - Define the User entity/model with all required fields and relationships.
  - Add validation decorators for required fields and unique constraints for username and email.
  - Set up relations with the Role entity (Many-to-One).
  - Integrate with the ORM (TypeORM/Prisma) and database migration.
  - Add repository/service for basic CRUD operations (for internal use).
- [x] **[Sub] Backend: Create User Entity Migration** [#48](https://github.com/tan-interspace/pay-per-post/issues/48)
  - Write and test the migration for the User entity. ([PR #182](https://github.com/tan-interspace/pay-per-post/pull/182))
- [x] **[Sub] Backend: Implement User CRUD Service & Controller** [#49](https://github.com/tan-interspace/pay-per-post/issues/49)
  - Implement CRUD service and controller for User.
- [x] **[Sub] Backend: Integrate OpenAPI/Swagger for Backend API Documentation** [#160](https://github.com/tan-interspace/pay-per-post/issues/160)
  - Add OpenAPI/Swagger for Back
  - [x] Add Swagger categories for User, Role, Campaign endpoints ([PR #177](https://github.com/tan-interspace/pay-per-post/pull/177))
- [x] **[Sub] Backend: Write Unit & Integration Tests for User Entity** [#50](https://github.com/tan-interspace/pay-per-post/issues/50)
  - Write unit and integration tests for User entity, service, and controller.
- [x] **Backend: Design and Implement Role Entity/Model (NestJS)** [#2](https://github.com/tan-interspace/pay-per-post/issues/2)
  - Define the Role entity/model with all required fields and relationships.
  - Add validation for required fields and unique constraint on name.
  - Set up relation with the User entity (One-to-Many).
  - Integrate with ORM (TypeORM/Prisma) và migration.
  - Add repository/service for CRUD operations (for internal use).
- [x] **[Sub] Backend: Create Role Entity Migration** [#52](https://github.com/tan-interspace/pay-per-post/issues/52)
  - Write and test the migration for the Role entity.
- [x] **[Sub] Backend: Implement Role CRUD Service & Controller** [#53](https://github.com/tan-interspace/pay-per-post/issues/53)
  - Implement CRUD service and controller for Role.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Role Entity** [#54](https://github.com/tan-interspace/pay-per-post/issues/54)
  - Write unit and integration tests for Role entity, service, and controller.
- [x] **[Sub] Backend: Create Seed Data for Roles** [#55](https://github.com/tan-interspace/pay-per-post/issues/55)
  - Define and implement seed data for all required roles.
- [x] **[Sub] Backend: Create Seed Data for Sample Users** [#56](https://github.com/tan-interspace/pay-per-post/issues/56)
  - Define and implement seed data for sample users, each assigned to a role.
- [x] **[Sub] Backend: Implement Database Seed Script** [#57](https://github.com/tan-interspace/pay-per-post/issues/57)
  - Write a script to insert roles and users into the database.
- [x] **[Sub] Backend: Document Seeding Process** [#58](https://github.com/tan-interspace/pay-per-post/issues/58)
  - Add documentation to the README on how to run the seed script.
- [x] **[Sub] Backend: Connect to Supabase.com as a Managed Database** [#168]
  - [x] [Backend: Create Supabase Project (#169)](https://github.com/tan-interspace/pay-per-post/issues/169)
  - [x] [Backend: Configure Supabase Database (#170)](https://github.com/tan-interspace/pay-per-post/issues/170)
  - [x] [Backend: Update Database Connection to Supabase (#171)](https://github.com/tan-interspace/pay-per-post/issues/171)
  - [x] [Backend: Test Database Connection to Supabase (#172)](https://github.com/tan-interspace/pay-per-post/issues/172)
  - [x] [Backend: Update Documentation for Supabase Integration (#173)](https://github.com/tan-interspace/pay-per-post/issues/173)
  - [x] [Backend: Data Migration to Supabase (Optional) (#174)](https://github.com/tan-interspace/pay-per-post/issues/174)
  - [x] [Backend: Update Docker Compose for Supabase (#175)](https://github.com/tan-interspace/pay-per-post/issues/175)

## 2. Authentication & Authorization

- [x] **[Sub] Backend: Implement Login Endpoint & Password Hashing** [#59](https://github.com/tan-interspace/pay-per-post/issues/59)
  - Create login endpoint, implement password hashing and validation.
- [x] **[Sub] Backend: Integrate JWT Authentication** [#60](https://github.com/tan-interspace/pay-per-post/issues/60)
  - Integrate JWT for authentication, generate and validate tokens.
- [x] **[Sub] Backend: Protect Routes with JWT Guard** [#61](https://github.com/tan-interspace/pay-per-post/issues/61)
  - Implement JWT guard to protect private routes.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Authentication** [#62](https://github.com/tan-interspace/pay-per-post/issues/62)
  - Write tests for login, JWT logic, and route protection.
- [x] **[Sub] Backend: Implement RBAC Guard** [#63](https://github.com/tan-interspace/pay-per-post/issues/63)
  - Implement a guard to check user roles and restrict access to endpoints.
- [x] **[Sub] Backend: Create Role Decorator** [#64](https://github.com/tan-interspace/pay-per-post/issues/64)
  - Create a custom decorator to specify required roles for routes/handlers.
- [x] **[Sub] Backend: Apply RBAC Protection to Endpoints** [#65](https://github.com/tan-interspace/pay-per-post/issues/65)
  - Apply RBAC guard and decorator to all relevant endpoints.
- [x] **[Sub] Backend: Write Unit & Integration Tests for RBAC** [#66](https://github.com/tan-interspace/pay-per-post/issues/66)
  - Write tests for RBAC guard, decorator, and protected endpoints.
- [x] **[Sub] Frontend: Design & Implement Login UI** [#67](https://github.com/tan-interspace/pay-per-post/issues/67) <!-- done dev -->
  - Create a responsive login form with validation and error handling.
- [x] **[Sub] Frontend: Integrate Login UI with Backend API** [#68](https://github.com/tan-interspace/pay-per-post/issues/68) <!-- done dev -->
  - Connect the login form to the backend authentication endpoint, handle JWT/token on success.
- [x] **[Sub] Frontend: Implement JWT Storage & Session Management** [#69](https://github.com/tan-interspace/pay-per-post/issues/69) <!-- done dev -->
  - Store JWT securely, manage session state.
- [x] **[Sub] Frontend: Implement Role-Based Navigation & Route Protection** [#70](https://github.com/tan-interspace/pay-per-post/issues/70) <!-- done dev -->
  - Show/hide routes/components based on user role, protect private routes.
- [x] **Backend: Implement Authentication Module with JWT Strategy** [#3](https://github.com/tan-interspace/pay-per-post/issues/3)
  - Create authentication module and service in NestJS.
  - Implement login endpoint (POST /auth/login) that validates user credentials.
  - On successful login, generate and return a JWT.
  - Use bcrypt or argon2 for password verification.
  - Implement JWT strategy for protecting private routes.
  - Add error handling for invalid credentials and locked/inactive users.
- [x] **Backend: Implement Role-Based Access Control (RBAC) Guards and Decorators** [#4](https://github.com/tan-interspace/pay-per-post/issues/4)
  - Create custom NestJS guards and/or decorators for role-based access control.
  - Integrate RBAC checks into protected API routes.
  - Ensure RBAC logic is reusable and easy to extend for new roles or permissions.
  - Add unit tests for RBAC logic and protected endpoints.

## 3. Campaign Management

- [x] **[Sub] Backend: Design and Implement Campaign Entity/Model (NestJS)** [#6](https://github.com/tan-interspace/pay-per-post/issues/6)
  - Define the Campaign entity/model with all required fields and relationships.
  - Add validation decorators for required fields and unique constraints as needed.
  - Set up relations with the User entity (Advertiser) and other relevant entities.
  - Integrate with the ORM (TypeORM/Prisma) và migration.
  - Add repository/service for basic CRUD operations (for internal use).
  - Add validation decorators for required fields and unique constraints as needed.
  - Set up relations with the User entity (Advertiser) and other relevant entities.
  - Integrate with the ORM (TypeORM/Prisma) và migration.
  - Add repository/service for basic CRUD operations (for internal use).
- [x] **[Sub] Backend: Create Campaign Entity Migration** [#72](https://github.com/tan-interspace/pay-per-post/issues/72)
  - Create and run the migration for the Campaign entity in the database.
- [x] **[Sub] Backend: Implement Campaign CRUD Endpoints** [#73](https://github.com/tan-interspace/pay-per-post/issues/73)
  - Implement Create, Read, Update, Delete endpoints for Campaign.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Campaign** [#74](https://github.com/tan-interspace/pay-per-post/issues/74)
  - Write tests for the Campaign entity, service, and controller.
- [x] **[Sub] Backend: Implement Create Campaign Service Logic** [#75](https://github.com/tan-interspace/pay-per-post/issues/75)
  - Implement the service method to handle campaign creation, including validation and business logic.
- [x] **[Sub] Backend: Implement Create Campaign Controller Endpoint (POST /campaigns)** [#76](https://github.com/tan-interspace/pay-per-post/issues/76)
  - Implement the controller route to expose the POST /campaigns endpoint, connect it to the service, and handle request/response DTOs.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Create Campaign Endpoint** [#77](https://github.com/tan-interspace/pay-per-post/issues/77)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [x] **[Sub] Backend: Implement Update Campaign Service Logic** [#78](https://github.com/tan-interspace/pay-per-post/issues/78)
  - Implement the service method to handle updating a campaign, including validation and business logic.
- [x] **[Sub] Backend: Implement Update Campaign Controller Endpoint (PUT /campaigns/:id)** [#79](https://github.com/tan-interspace/pay-per-post/issues/79)
  - Implement the controller route to expose the PUT /campaigns/:id endpoint, connect it to the service, and handle request/response DTOs.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Update Campaign Endpoint** [#80](https://github.com/tan-interspace/pay-per-post/issues/80)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [x] **[Sub] Backend: Implement Delete Campaign Service Logic** [#81](https://github.com/tan-interspace/pay-per-post/issues/81)
  - Implement the service method to handle deleting a campaign, including validation and business logic.
- [x] **[Sub] Backend: Implement Delete Campaign Controller Endpoint (DELETE /campaigns/:id)** [#82](https://github.com/tan-interspace/pay-per-post/issues/82)
  - Implement the controller route to expose the DELETE /campaigns/:id endpoint, connect it to the service, and handle request/response DTOs.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Delete Campaign Endpoint** [#83](https://github.com/tan-interspace/pay-per-post/issues/83)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [x] **[Sub] Backend: Implement List Active Campaigns Service Logic** [#84](https://github.com/tan-interspace/pay-per-post/issues/84)
  - Implement the service method to fetch and filter active campaigns for publishers, including any business logic (e.g., status, date, eligibility).
- [x] **[Sub] Backend: Implement List Active Campaigns Controller Endpoint (GET /campaigns/publisher)** [#85](https://github.com/tan-interspace/pay-per-post/issues/85)
  - Implement the controller route to expose the GET /campaigns/publisher endpoint, connect it to the service, and handle request/response DTOs.
- [x] **[Sub] Backend: Write Unit & Integration Tests for List Active Campaigns Endpoint** [#86](https://github.com/tan-interspace/pay-per-post/issues/86)
  - Write tests to ensure the endpoint and service logic work as expected, including filtering, validation, and error handling.
- [x] **[Sub] Backend: Implement Get Campaign Detail Service Logic** [#87](https://github.com/tan-interspace/pay-per-post/issues/87)
  - Implement the service method to fetch campaign details by ID, including any business logic (e.g., access control, related data).
- [x] **[Sub] Backend: Implement Get Campaign Detail Controller Endpoint (GET /campaigns/:id)** [#88](https://github.com/tan-interspace/pay-per-post/issues/88)
  - Implement the controller route to expose the GET /campaigns/:id endpoint, connect it to the service, and handle request/response DTOs.
- [x] **[Sub] Backend: Write Unit & Integration Tests for Get Campaign Detail Endpoint** [#89](https://github.com/tan-interspace/pay-per-post/issues/89)
  - Write tests to ensure the endpoint and service logic work as expected, including validation, error handling, and edge cases.
- [x] **[Sub] Frontend: Design & Implement Publisher Campaign List UI** [#90](https://github.com/tan-interspace/pay-per-post/issues/90)
  - Design and implement a responsive UI for listing campaigns, including loading, empty, and error states.
- [x] **[Sub] Frontend: Integrate Campaign List UI with Backend API** [#91](https://github.com/tan-interspace/pay-per-post/issues/91)
  - Connect the UI to the backend endpoint for fetching campaigns, handle data fetching, and display results.
- [ ] **[Sub] Frontend: Implement Filtering, Pagination, and Search for Campaign List** [#92](https://github.com/tan-interspace/pay-per-post/issues/92)
  - Add filtering, pagination, and search functionality to the campaign list page.
- [x] **[Sub] Frontend: Write Unit & Integration Tests for Publisher Campaign List Page** [#93](https://github.com/tan-interspace/pay-per-post/issues/93)
  - Write tests to ensure the UI and data integration work as expected, including edge cases.
- [x] **[Sub] Frontend: Design & Implement Publisher Campaign Detail UI** [#94](https://github.com/tan-interspace/pay-per-post/issues/94)
  - Design and implement a responsive UI for displaying campaign details, including loading and error states.
- [x] **[Sub] Frontend: Integrate Campaign Detail UI with Backend API** [#95](https://github.com/tan-interspace/pay-per-post/issues/95)
  - Connect the UI to the backend endpoint for fetching campaign details, handle data fetching, and display results.
- [x] **[Sub] Frontend: Implement Apply to Campaign Functionality** [#96](https://github.com/tan-interspace/pay-per-post/issues/96)
  - Add an "Apply" button and logic to allow publishers to apply for a campaign, including feedback for success/failure.
- [x] **[Sub] Frontend: Write Unit & Integration Tests for Publisher Campaign Detail Page** [#97](https://github.com/tan-interspace/pay-per-post/issues/97)
  - Write tests to ensure the UI, data integration, and apply functionality work as expected, including edge cases.
- [ ] **[Sub] Backend: Update Application Entity/Model for Approval Workflow** [#98](https://github.com/tan-interspace/pay-per-post/issues/98)
  - Add status, reviewer, and feedback fields to the Application entity/model.
- [ ] **[Sub] Backend: Create & Run Migration for Application Approval Fields** [#99](https://github.com/tan-interspace/pay-per-post/issues/99)
  - Create and run a database migration to add the new fields to the Application table.
- [ ] **[Sub] Backend: Update Application DTOs & Validation for Approval Fields** [#100](https://github.com/tan-interspace/pay-per-post/issues/100)
  - Update DTOs and validation logic to support the new fields in API requests/responses.
- [ ] **[Sub] Backend: Write Unit & Integration Tests for Application Approval Workflow** [#101](https://github.com/tan-interspace/pay-per-post/issues/101)
  - Write tests to ensure the new fields and logic work as expected.
- [ ] **[Sub] Backend: Implement Apply to Campaign Service Logic** [#102](https://github.com/tan-interspace/pay-per-post/issues/102)
  - Implement the service method to handle the application logic, including validation and business rules.
- [ ] **[Sub] Backend: Implement Apply to Campaign Controller Endpoint (POST /applications)** [#103](https://github.com/tan-interspace/pay-per-post/issues/103)
  - Implement the controller route to expose the POST /applications endpoint, connect it to the service, and handle request/response DTOs.
- [ ] **[Sub] Backend: Write Unit & Integration Tests for Apply to Campaign Endpoint** [#104](https://github.com/tan-interspace/pay-per-post/issues/104)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [ ] **[Sub] Backend: Implement SP Team Review Service Logic** [#105](https://github.com/tan-interspace/pay-per-post/issues/105)
  - Implement the service method to handle SP Team review actions (approve/reject), including validation and business rules.
- [ ] **[Sub] Backend: Implement SP Team Review Controller Endpoint (PUT /applications/:id/review)** [#106](https://github.com/tan-interspace/pay-per-post/issues/106)
  - Implement the controller route to expose the PUT /applications/:id/review endpoint, connect it to the service, and handle request/response DTOs.
- [ ] **[Sub] Backend: Write Unit & Integration Tests for SP Team Review Endpoint** [#107](https://github.com/tan-interspace/pay-per-post/issues/107)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [ ] **[Sub] Backend: Implement Advertiser Review Service Logic** [#108](https://github.com/tan-interspace/pay-per-post/issues/108)
  - Implement the service method to handle Advertiser review actions (approve/reject), including validation and business rules.
- [ ] **[Sub] Backend: Implement Advertiser Review Controller Endpoint (PUT /applications/:id/advertiser-review)** [#109](https://github.com/tan-interspace/pay-per-post/issues/109)
  - Implement the controller route to expose the PUT /applications/:id/advertiser-review endpoint, connect it to the service, and handle request/response DTOs.
- [ ] **[Sub] Backend: Write Unit & Integration Tests for Advertiser Review Endpoint** [#110](https://github.com/tan-interspace/pay-per-post/issues/110)
  - Write tests to ensure the endpoint and service logic work as expected, including validation and error handling.
- [ ] **[Sub] Frontend: Design & Implement SP Team Application Review UI** [#111](https://github.com/tan-interspace/pay-per-post/issues/111)
  - Design and implement a responsive UI for listing and reviewing applications, including loading, empty, and error states.
- [ ] **[Sub] Frontend: Integrate SP Team Review UI with Backend API** [#112](https://github.com/tan-interspace/pay-per-post/issues/112)
  - Connect the UI to the backend endpoint for fetching and updating application review status, handle data fetching, and display results.
- [ ] **[Sub] Frontend: Implement Approve/Reject Actions for Applications** [#113](https://github.com/tan-interspace/pay-per-post/issues/113)
  - Add approve/reject buttons and logic to allow the SP Team to review applications, including feedback for success/failure.
- [ ] **[Sub] Frontend: Write Unit & Integration Tests for SP Team Application Review Page** [#114](https://github.com/tan-interspace/pay-per-post/issues/114)
  - Write tests to ensure the UI, data integration, and review actions work as expected, including edge cases.
- [ ] **[Sub] Frontend: Design & Implement Advertiser Application Review UI** [#115](https://github.com/tan-interspace/pay-per-post/issues/115)
  - Design and implement a responsive UI for the Advertiser to list and review publisher applications, including loading, empty, and error states.
- [ ] **[Sub] Frontend: Integrate Advertiser Review UI with Backend API** [#116](https://github.com/tan-interspace/pay-per-post/issues/116)
  - Connect the UI to the backend endpoint for fetching and updating application review status, handle data fetching, and display results.
- [ ] **[Sub] Frontend: Implement Approve/Reject Actions for Advertiser Applications** [#117](https://github.com/tan-interspace/pay-per-post/issues/117)
  - Add approve/reject buttons and logic to allow the Advertiser to review applications, including feedback for success/failure.
- [ ] **[Sub] Frontend: Write Unit & Integration Tests for Advertiser Application Review Page** [#118](https://github.com/tan-interspace/pay-per-post/issues/118)
  - Write tests to ensure the UI, data integration, and review actions work as expected, including edge cases.
- [ ] **[Sub] Frontend: Design & Implement Publisher Application Status UI** [#119](https://github.com/tan-interspace/pay-per-post/issues/119)
  - Design and implement a responsive UI for publishers to view the status of their campaign applications, including loading, empty, and error states.
- [ ] **[Sub] Frontend: Integrate Application Status UI with Backend API** [#120](https://github.com/tan-interspace/pay-per-post/issues/120)
  - Connect the UI to the backend endpoint for fetching application statuses, handle data fetching, and display results.
- [ ] **[Sub] Frontend: Implement Status Indicators & Filtering for Applications** [#121](https://github.com/tan-interspace/pay-per-post/issues/121)
  - Add clear status indicators (e.g., pending, approved, rejected) and filtering options for the list of applications.
- [ ] **[Sub] Frontend: Write Unit & Integration Tests for Publisher Application Status Page** [#122](https://github.com/tan-interspace/pay-per-post/issues/122)
  - Write tests to ensure the UI, data integration, and status logic work as expected, including edge cases.

## 4. Application Workflow

- [ ] **[Sub] Backend: Integrate TikTok Video Link Submission** [#123](https://github.com/tan-interspace/pay-per-post/issues/123)
  - Implement backend logic for publishers to submit TikTok video links (instead of file uploads).
  - Validate TikTok link format and store in VideoSubmission entity.
- [ ] **[Sub] Backend: Implement TikTok Video Review Service Logic** [#128](https://github.com/tan-interspace/pay-per-post/issues/128)
  - Implement service logic for reviewing TikTok video links, updating status, and storing feedback.
- [ ] **[Sub] Backend: Implement TikTok Video Review Controller Endpoint (PUT /videos/:id/review)** [#129](https://github.com/tan-interspace/pay-per-post/issues/129)
  - Create the controller endpoint for SP Team and Advertiser to review TikTok video links, update status, and provide feedback.
- [ ] **[Sub] Backend: Implement TikTok Link Submission Endpoint (PUT /videos/:id/tiktok-link)** [#130](https://github.com/tan-interspace/pay-per-post/issues/130)
  - Create the endpoint for publishers to submit or update the TikTok video link for an approved application.
- [ ] **[Sub] Backend: Update VideoSubmission DTOs & Validation for TikTok Link/Review/Feedback** [#131](https://github.com/tan-interspace/pay-per-post/issues/131)
  - Update DTOs and validation logic to support TikTok link, review status, and feedback fields.
- [x] **[Sub] Backend: Write Unit & Integration Tests for TikTok Video Link & Review APIs** [#132](https://github.com/tan-interspace/pay-per-post/issues/132)
  - All unit tests for VideoSubmission service/controller, review, feedback, and endpoint have been written and passed. Test coverage is sufficient, code has been merged or is pending merge.
- [ ] **[Sub] Frontend: Design & Implement Publisher TikTok Link Submission UI** [#133](https://github.com/tan-interspace/pay-per-post/issues/133)
  - Design and implement a responsive UI for publishers to submit TikTok video links, including validation, loading, empty, and error states.
- [ ] **[Sub] Frontend: Integrate TikTok Link Submission UI with Backend API** [#134](https://github.com/tan-interspace/pay-per-post/issues/134)
  - Connect the UI to the backend endpoint for submitting TikTok links, handle data fetching, and display results.
- [ ] **[Sub] Frontend: Implement Resubmission & TikTok Link Update Logic** [#135](https://github.com/tan-interspace/pay-per-post/issues/135)
  - Add logic for resubmitting or updating TikTok links, including validation and user feedback.
- [ ] **[Sub] Frontend: Write Unit & Integration Tests for Publisher TikTok Link Submission Page** [#136](https://github.com/tan-interspace/pay-per-post/issues/136)
  - Write tests to ensure the UI, data integration, and TikTok link submission logic work as expected, including edge cases.
- [ ] **Frontend: SP Team TikTok Video Review UI** [#34](https://github.com/tan-interspace/pay-per-post/issues/34)
  - UI for SP Team to review TikTok video links, provide feedback, and update status.
- [ ] **Frontend: Advertiser TikTok Video Review UI** [#35](https://github.com/tan-interspace/pay-per-post/issues/35)
  - UI for Advertiser to review TikTok video links, provide feedback, and update status.

## 5. Video Management
- [ ] **[Sub] Backend: Implement TikTok OAuth Endpoints** [#301](https://github.com/tan-interspace/pay-per-post/issues/301)
  - Implement `/api/tiktok/connect-url` and `/api/tiktok/callback` endpoints for TikTok OAuth flow.
- [ ] **[Sub] Backend: Implement TikTok Token Storage** [#301](https://github.com/tan-interspace/pay-per-post/issues/301)
  - Store TikTok tokens and account info in `tiktok_accounts` table after OAuth.
- [ ] **[Sub] Backend: Implement TikTok Video Sync Endpoints** [#301](https://github.com/tan-interspace/pay-per-post/issues/301)
  - Implement `/api/tiktok/videos` and `/api/tiktok/publisher/:publisherId/videos` endpoints for fetching and storing TikTok videos.
- [ ] **[Sub] Backend: Add Prisma Migration for TikTok Tables** [#301](https://github.com/tan-interspace/pay-per-post/issues/301)
  - Add and run migration for `tiktok_accounts` and `tiktok_videos` tables in Prisma schema.
- [ ] **Backend: Implement TikTok Integration (OAuth, Video Sync, Endpoints)** [#301](https://github.com/tan-interspace/pay-per-post/issues/301)
  - Implement TikTok integration on the backend:
    - Create endpoints: `/api/tiktok/connect-url`, `/api/tiktok/callback`, `/api/tiktok/videos`, `/api/tiktok/publisher/:publisherId/videos`.
    - Implement TikTok OAuth flow: generate connect URL, handle callback, exchange code for tokens, store tokens.
    - Use the database schema: `tiktok_accounts` (user_id, tiktok_user_id, tokens, expiry, connected_at), `tiktok_videos` (video_id, account reference, metadata, stats, fetched_at).
    - Do not implement front-end or staff dashboard.
    - Update migration and Prisma schema as needed.

- [ ] **Backend: Implement Status Tracking for Application & TikTok VideoSubmission** [#27](https://github.com/tan-interspace/pay-per-post/issues/27)
  - Add status tracking for Application and TikTok VideoSubmission (pending, approved, rejected, etc.).
- [ ] **Backend: Implement Notification Service for Status Changes** [#28](https://github.com/tan-interspace/pay-per-post/issues/28)
  - Email notification module for status changes (e.g., TikTok link reviewed, approved, or rejected).
- [x] **Frontend: Implement Status Tracking & Notification UI** [#29](https://github.com/tan-interspace/pay-per-post/issues/29)
   - UI to display status and notifications on dashboard, including TikTok video review status.
   - Implemented on feature/fe-218-221-ui-improvements branch.

## 6. Status Tracking & Notification

- [x] **Frontend: Apply Consistent Styling Across Application** [#16](https://github.com/tan-interspace/pay-per-post/issues/16)
   - Unify style, use design system.
   - Implemented on feature/fe-218-221-ui-improvements branch.
- [x] **Frontend: Ensure Basic Responsiveness for Key Dashboard Views** [#17](https://github.com/tan-interspace/pay-per-post/issues/17)
   - Ensure dashboard responsiveness.
   - Implemented on feature/fe-218-221-ui-improvements branch.
- [x] **Frontend: Improve User Feedback Mechanisms (Loading, Success, Error States)** [#18](https://github.com/tan-interspace/pay-per-post/issues/18)
   - Clear loading, success, and error messages.
   - Implemented on feature/fe-218-221-ui-improvements branch.

## 7. UI/UX Polish & Responsive

- [ ] **MVP Deployment** [#36](https://github.com/tan-interspace/pay-per-post/issues/36)
  - Deploy to Vercel/Heroku/AWS, perform smoke test for all features.
- [x] **[QA] Backend: User & Role Entity/CRUD/Seed/Test** [#137](https://github.com/tan-interspace/pay-per-post/issues/137)
  - Ensure all User & Role CRUD features, seed/migration, API testing, and data verification are complete.
- [x] **[QA] Backend: Authentication & RBAC** [#138](https://github.com/tan-interspace/pay-per-post/issues/138)
  - Ensure authentication, authorization, and route protection features work correctly and securely. All test cases pass, no critical bugs.
- [x] **Setup GitHub Actions CI for Frontend & Backend PRs** [#180](https://github.com/tan-interspace/pay-per-post/issues/180)
  - Auto run test ([PR #181](https://github.com/tan-interspace/pay-per-post/pull/181))
- [x] **Backend: Create Initial Seed Data for User Roles** [#5](https://github.com/tan-interspace/pay-per-post/issues/5)
   - Define seed data for roles: Publisher, SP Team, Advertiser.
   - Define seed data for at least one user per role (with secure password hashes).
   - Implement a database seeding script or migration for inserting roles and users.
   - Document how to run the seeding process in the project README.
- [ ] **Frontend: Refactor UI from MUI to Tailwind CSS** [#207](https://github.com/tan-interspace/pay-per-post/issues/207)
   - Refactor the entire frontend UI to use Tailwind CSS instead of Material UI (MUI). Remove all MUI dependencies, use Tailwind utility classes, ensure full feature parity and responsive design, and update documentation if needed. 
  - Define seed data for roles: Publisher, SP Team, Advertiser.
  - Define seed data for at least one user per role (with secure password hashes).
  - Implement a database seeding script or migration for inserting roles and users.
  - Document how to run the seeding process in the project README.

## TikTok Integration (OAuth, Video Sync, Monitoring)

- [ ] **[Epic] TikTok Integration: OAuth, Video Sync, and Monitoring**
  - This epic tracks all tasks required to integrate TikTok API into the platform, including OAuth flow, video sync, and staff monitoring features for publishers' TikTok accounts.
- [ ] **Register TikTok App and Configure Credentials**
  - Register the app on TikTok for Developers, store client ID/secret and redirect URIs in environment variables.
- [ ] **Add TikTok Account and Video Tables to Prisma Schema**
  - Update prisma/schema.prisma to add tiktok_accounts and tiktok_videos tables, run migration.
- [ ] **Implement TikTok OAuth URL Generation Endpoint**
  - Endpoint: GET /api/tiktok/connect-url. Returns TikTok OAuth URL for publisher to start connection.
- [ ] **Implement TikTok OAuth Callback Endpoint**
  - Endpoint: GET /api/tiktok/callback. Handles TikTok redirect, exchanges code for tokens, stores tokens and TikTok user info.
- [ ] **Implement Token Refresh Logic**
  - Service to refresh TikTok tokens when expired.
- [ ] **Implement Endpoint to Fetch Publisher's TikTok Videos**
  - Endpoint: GET /api/tiktok/videos. Fetches videos from TikTok API using stored tokens.
- [ ] **Implement Endpoint for Staff to Fetch Publisher's TikTok Videos**
  - Endpoint: GET /api/tiktok/publisher/:publisherId/videos. Staff can fetch videos for any connected publisher.
- [ ] **Implement Endpoint to Disconnect TikTok Account**
  - Endpoint: POST /api/tiktok/disconnect. Removes TikTok tokens and account info for publisher.
- [ ] **Cache TikTok Video Metadata (Optional)**
  - Store fetched video metadata for faster staff access.
- [ ] **Write Unit & Integration Tests for All TikTok Endpoints**
  - Tests for OAuth, token storage, video fetch, disconnect, and error handling.
- [ ] **Add "Connect TikTok" Button to Publisher Dashboard**
  - UI button triggers OAuth flow.
- [ ] **Handle TikTok OAuth Redirect in Front-end**
  - After OAuth, update UI based on connection status.
- [ ] **Display Connected TikTok Account Info**
  - Show TikTok username, avatar, and disconnect option.
- [ ] **Display Publisher's TikTok Video List**
  - Fetch and display videos (thumbnail, title, date, views, etc.).
- [ ] **Add "Disconnect TikTok" Button**
  - Allows publisher to disconnect their TikTok account.
- [ ] **Staff Dashboard: List Publishers and TikTok Connection Status**
  - Staff can see which publishers are connected to TikTok.
- [ ] **Staff Dashboard: View TikTok Videos for Any Publisher**
  - Staff can view video list for any connected publisher.
- [ ] **Loading, Error, and Empty States for All TikTok UI**
  - Show appropriate UI for loading, errors, or no data.
- [ ] **Write Unit & Integration Tests for TikTok UI**
  - Tests for all new UI components and flows.
