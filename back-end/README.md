<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ npm install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).

# Pay Per Post Backend (NestJS + Prisma)

## 1. Installation
```bash
cd back-end/api
npm install
```

## 2. Environment Variables
Create a `.env` file based on the provided `.env.example`:
```
cp .env.example .env
```
Fill in your PostgreSQL connection details in the `DATABASE_URL` variable.

## 3. Database Migration with Prisma
```bash
npx prisma migrate dev --name init
```

## 4. Run the Application Locally
```bash
npm run start:dev
```

## 5. API Documentation (Swagger)
After starting the app, access: [http://localhost:3000/api](http://localhost:3000/api)

- The Swagger UI provides interactive API documentation for all backend endpoints.
- Use it to explore, test, and understand the API contract.

## 6. Useful Prisma Commands
- Create a new migration: `npx prisma migrate dev --name <name>`
- Open Prisma Studio: `npx prisma studio`
- Sync schema: `npx prisma generate`

## 7. Project Structure
- `src/` - NestJS source code
- `prisma/` - Prisma schema and migrations

## Docker-based Local Development

You can use Docker Compose to run a local PostgreSQL database for backend development. This is the recommended way to ensure consistency across environments.

### 1. Start PostgreSQL with Docker Compose

```bash
docker-compose up -d
```

This will start a Postgres database (and optionally pgAdmin) for local development.

### 2. Configure Environment Variables

Copy `.env.example` to `.env` and ensure `DATABASE_URL` matches the Docker Compose service (see below).

### 3. Run Migrations

```bash
npx prisma migrate dev --name init
```

### 4. Start the Backend

```bash
npm run start:dev
```

---
For any setup questions, please refer to the documentation or contact the backend team!

# Supabase Integration

## 1. Supabase Project Setup
- The production database is managed by [Supabase](https://supabase.com/).
- Project: [pay-per-post Supabase](https://app.supabase.com/project/db.jitapccgwurwbagugvjy)
- Region, password, and other settings are managed in the Supabase dashboard.

## 2. Database Connection
- Get your connection string from Supabase: **Project Settings → Database → Connection string**.
- Example `.env` entry:
  ```
  DATABASE_URL="postgresql://postgres:<password>@db.jitapccgwurwbagugvjy.supabase.co:5432/postgres"
  ```
- Never commit your real password to git!

## 3. Running Migrations on Supabase
- To apply Prisma migration SQL files to Supabase, use the following command:
  ```sh
  psql "host=db.jitapccgwurwbagugvjy.supabase.co port=5432 dbname=postgres user=postgres password=<your_password> sslmode=require" -f back-end/api/prisma/migrations/<migration_folder>/<migration.sql>
  ```
- Example for user and campaign migrations:
  ```sh
  psql "host=db.jitapccgwurwbagugvjy.supabase.co port=5432 dbname=postgres user=postgres password=<your_password> sslmode=require" -f back-end/api/prisma/migrations/20250627082938_user_entity_init/migration.sql
  psql "host=db.jitapccgwurwbagugvjy.supabase.co port=5432 dbname=postgres user=postgres password=<your_password> sslmode=require" -f back-end/api/prisma/migrations/20250627091308_campaign_entity_init/migration.sql
  ```
- You need `psql` installed (see https://www.postgresql.org/download/ or use Homebrew: `brew install libpq && brew link --force libpq`).

## 4. Local vs. Production
- For local development, you can still use Docker Compose with a local Postgres instance.
- For production/staging, use the Supabase connection string in your `.env`.
- Switch between environments by changing the `DATABASE_URL` in your `.env` file.

## 5. Troubleshooting
- **SSL errors:** Ensure `sslmode=require` is in your connection string.
- **Permission errors:** Use the default `postgres` user and your Supabase password.
- **psql not found:** Install and link `libpq` as described above.

## 6. Data Migration
- To migrate data from another Postgres instance, use `pg_dump` and `pg_restore`:
  ```sh
  pg_dump -U <old_user> -h <old_host> -p <old_port> -Fc -d <old_db> -f backup.dump
  pg_restore --no-owner -U postgres -h db.jitapccgwurwbagugvjy.supabase.co -p 5432 -d postgres -W backup.dump
  ```
- Always back up your data before migrating.

---
