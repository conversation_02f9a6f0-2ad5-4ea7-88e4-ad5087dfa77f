# Applications Entity Database Schema

## Table: applications

| Field        | Type           | Constraints                  | Description                        |
|--------------|----------------|------------------------------|------------------------------------|
| id           | UUID           | PK, not null, unique         | Application ID                     |
| campaign_id  | UUID           | FK → campaigns.id, not null  | Campaign being applied to          |
| publisher_id | UUID           | FK → users.id, not null      | Publisher (user) who applies       |
| status       | VARCHAR(50)    | not null                     | Status (pending, approved, rejected, etc.)|
| reviewer_id  | UUID           | FK → users.id, nullable      | Reviewer (SP Team/Advertiser)      |
| feedback     | TEXT           | nullable                     | Feedback from reviewer             |
| created_at   | TIMESTAMP      | not null, default now()      | Creation timestamp                 |
| updated_at   | TIMESTAMP      | not null, default now()      | Last update timestamp              |

## Example: PostgreSQL Migration
```sql
CREATE TABLE applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID NOT NULL REFERENCES campaigns(id),
  publisher_id UUID NOT NULL REFERENCES users(id),
  status VARCHAR(50) NOT NULL,
  reviewer_id UUID REFERENCES users(id),
  feedback TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## Example: Prisma Schema
```prisma
model Application {
  id           String   @id @default(uuid())
  campaign     Campaign @relation(fields: [campaignId], references: [id])
  campaignId   String
  publisher    User     @relation(fields: [publisherId], references: [id])
  publisherId  String
  status       String
  reviewer     User?    @relation(fields: [reviewerId], references: [id])
  reviewerId   String?
  feedback     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  videoSubmissions VideoSubmission[]
}
```

## Notes
- `campaign_id` links to the campaigns table.
- `publisher_id` links to the users table (role = publisher).
- `reviewer_id` is the user who performs the review (SP Team or Advertiser), can be null if not yet reviewed.
- Status: pending, approved, rejected, ... 