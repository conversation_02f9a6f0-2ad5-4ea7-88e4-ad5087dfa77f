import { PrismaClient } from '@prisma/client';
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

async function main() {
  // Clean up TikTok tables before seeding to avoid unique constraint errors
  await prisma.tiktokVideo.deleteMany();
  await prisma.tiktokAccount.deleteMany();

  // Seed roles
  const roles = [
    {
      id: 'admin',
      name: 'Admin',
      description: 'System administrator',
      permissions: ['manage_users', 'manage_roles', 'manage_campaigns', 'view_reports', 'full_access'],
    },
    {
      id: 'publisher',
      name: 'Publisher',
      description: 'User who applies to campaigns',
      permissions: ['apply_campaign', 'view_campaign', 'submit_video'],
    },
    {
      id: 'advertiser',
      name: 'Advertiser',
      description: 'User who creates campaigns',
      permissions: ['create_campaign', 'manage_own_campaigns', 'view_submissions'],
    },
    {
      id: 'sp_team',
      name: 'SP Team',
      description: 'Internal team for reviewing and approving applications',
      permissions: ['review_applications', 'manage_approvals', 'view_all_campaigns'],
    },
  ];

  for (const role of roles) {
    await prisma.role.upsert({
      where: { id: role.id },
      update: role,
      create: role,
    });
  }

  // Seed users
  const users = [
    { email: '<EMAIL>', name: 'Admin User', password: 'admin123', roleId: 'admin' },
    { email: '<EMAIL>', name: 'Advertiser One', password: 'advertiser123', roleId: 'advertiser' },
    { email: '<EMAIL>', name: 'Publisher One', password: 'publisher123', roleId: 'publisher' },
    { email: '<EMAIL>', name: 'SP Team User', password: 'spteam123', roleId: 'sp_team' },
    // Super admin for SP Team (full permissions, clearly marked)
    { email: '<EMAIL>', name: 'SP Team Super Admin', password: 'superadmin123', roleId: 'sp_team' }, // SUPER ADMIN USER
  ];

  for (const user of users) {
    const passwordHash = await bcrypt.hash(user.password, 10);
    await prisma.user.upsert({
      where: { email: user.email },
      update: {},
      create: {
        email: user.email,
        name: user.name,
        passwordHash,
        roleId: user.roleId,
      },
    });
  }
  
  // Seed notifications
  const seededUsers = await prisma.user.findMany({
    where: {
      email: {
        in: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ],
      },
    },
  });

  const notifications = [
    {
      userId: seededUsers.find(u => u.email === '<EMAIL>')?.id,
      type: 'system',
      title: 'Welcome Admin',
      message: 'This is a sample notification for the admin user.',
      data: { action: 'dashboard' },
      isRead: false,
      createdAt: new Date(),
    },
    {
      userId: seededUsers.find(u => u.email === '<EMAIL>')?.id,
      type: 'campaign',
      title: 'Campaign Approved',
      message: 'Your campaign has been approved.',
      data: { campaignId: 'sample-campaign-id' },
      isRead: false,
      createdAt: new Date(),
    },
    {
      userId: seededUsers.find(u => u.email === '<EMAIL>')?.id,
      type: 'submission',
      title: 'Submission Received',
      message: 'Your video submission was received.',
      data: { submissionId: 'sample-submission-id' },
      isRead: false,
      createdAt: new Date(),
    },
    {
      userId: seededUsers.find(u => u.email === '<EMAIL>')?.id,
      type: 'review',
      title: 'Review Assigned',
      message: 'You have been assigned a new review task.',
      data: { reviewId: 'sample-review-id' },
      isRead: false,
      createdAt: new Date(),
    },
  ].filter(n => n.userId); // Filter out if user not found

  if (notifications.length > 0) {
    await prisma.notification.createMany({
      data: notifications,
      skipDuplicates: true,
    });
  }

  // Seed campaigns
  const advertiserUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  if (advertiserUser) {
    await prisma.campaign.createMany({
      data: [
        {
          title: 'Summer Promo',
          description: 'Promote your brand this summer with our influencer network.',
          advertiserId: advertiserUser.id,
          startDate: new Date('2024-07-01'),
          endDate: new Date('2024-08-01'),
          status: 'active',
        },
        {
          title: 'Back to School Campaign',
          description: 'Engage students with special back-to-school offers.',
          advertiserId: advertiserUser.id,
          startDate: new Date('2024-08-15'),
          endDate: new Date('2024-09-15'),
          status: 'upcoming',
        },
        {
          title: 'Holiday Giveaway',
          description: 'Holiday season giveaway campaign for maximum reach.',
          advertiserId: advertiserUser.id,
          startDate: new Date('2024-12-01'),
          endDate: new Date('2024-12-31'),
          status: 'draft',
        },
      ],
      skipDuplicates: true,
    });
  }

  // Seed TikTok accounts and videos
  const publisherUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  if (publisherUser) {
    const tiktokAccount = await prisma.tiktokAccount.upsert({
      where: { tiktokUserId: 'mock_tiktok_user_id_1' },
      update: {},
      create: {
        userId: publisherUser.id,
        tiktokUserId: 'mock_tiktok_user_id_1',
        tokens: { access_token: 'mock_access_token', refresh_token: 'mock_refresh_token' },
        expiry: new Date(Date.now() + 1000 * 60 * 60 * 24), // 1 day from now
      },
    });
    await prisma.tiktokVideo.create({
      data: {
        videoId: 'mock_video_id_1',
        accountId: tiktokAccount.id,
        metadata: { title: 'Sample TikTok Video', cover_image_url: 'https://placekitten.com/400/300' },
        stats: { play_count: 1234, like_count: 56 },
      },
    });
  }

  // Seed notifications
  if (publisherUser) {
    await prisma.notification.createMany({
      data: [
        {
          userId: publisherUser.id,
          type: 'info',
          title: 'Welcome!',
          message: 'Thanks for joining as a publisher. Start applying to campaigns now!',
        },
        {
          userId: publisherUser.id,
          type: 'info',
          title: 'Campaign Update',
          message: 'A new campaign is available for you to join.',
        },
      ],
    });
  }
  if (advertiserUser) {
    await prisma.notification.createMany({
      data: [
        {
          userId: advertiserUser.id,
          type: 'info',
          title: 'Welcome!',
          message: 'Thanks for joining as an advertiser. Create your first campaign!',
        },
        {
          userId: advertiserUser.id,
          type: 'info',
          title: 'Submission Received',
          message: 'You have a new video submission to review.',
        },
      ],
    });
  }

  console.log('Seeded roles, users, and campaigns successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });