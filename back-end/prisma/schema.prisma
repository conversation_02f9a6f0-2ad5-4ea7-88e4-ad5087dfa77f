generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String     @id @default(uuid())
  email        String     @unique
  passwordHash String
  name         String
  roleId       String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  campaigns    Campaign[]
  role         Role       @relation(fields: [roleId], references: [id])
  publisherApplications Application[] @relation("PublisherApplications")
  reviewerApplications  Application[] @relation("ReviewerApplications")
  videoReviews          VideoSubmission[] @relation("VideoReviewer")
  tiktokAccounts        TiktokAccount[]
  notifications Notification[]
  assignedCampaigns CampaignPublisherAssignment[] @relation("AssignedPublisher")
  campaignAssignments CampaignPublisherAssignment[] @relation("AssignedBy")

}

model Role {
  id          String   @id
  name        String   @unique
  description String?
  permissions String[] @default([])
  users       User[]
}

model Campaign {
  id           String   @id @default(uuid())
  title        String
  description  String
  advertiserId String
  startDate    DateTime
  endDate      DateTime
  status       String
  assignedPublisherIds String[] @default([])
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  advertiser   User     @relation(fields: [advertiserId], references: [id])
  applications Application[]
  publisherAssignments CampaignPublisherAssignment[]
}

model Application {
  id           String   @id @default(uuid())
  campaignId   String
  publisherId  String
  status       String   @default("pending")
  reviewerId   String?
  feedback     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  campaign     Campaign @relation(fields: [campaignId], references: [id])
  publisher    User     @relation("PublisherApplications", fields: [publisherId], references: [id])
  reviewer     User?    @relation("ReviewerApplications", fields: [reviewerId], references: [id])
  videoSubmissions VideoSubmission[]
}

model TiktokAccount {
  id             String   @id @default(uuid())
  user           User     @relation(fields: [userId], references: [id])
  userId         String
  tiktokUserId   String   @unique
  tokens         Json
  expiry         DateTime
  connectedAt    DateTime @default(now())
  videos         TiktokVideo[]
}

model TiktokVideo {
  id           String         @id @default(uuid())
  videoId      String         @unique
  account      TiktokAccount  @relation(fields: [accountId], references: [id])
  accountId    String
  metadata     Json
  stats        Json
  fetchedAt    DateTime       @default(now())
}

model VideoSubmission {
  id             String      @id @default(uuid())
  applicationId  String
  tiktokLink     String
  status         String      @default("pending")
  reviewerId     String?
  feedback       String?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  application    Application @relation(fields: [applicationId], references: [id])
  reviewer       User?       @relation("VideoReviewer", fields: [reviewerId], references: [id])
}
model Notification {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  type      String
  title     String
  message   String
  data      Json?
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  readAt    DateTime?
  clearedAt DateTime?
}

model CampaignPublisherAssignment {
  id          String   @id @default(uuid())
  campaignId  String
  publisherId String
  assignedAt  DateTime @default(now())
  assignedBy  String
  isActive    Boolean  @default(true)
  campaign    Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  publisher   User     @relation("AssignedPublisher", fields: [publisherId], references: [id], onDelete: Cascade)
  assigner    User     @relation("AssignedBy", fields: [assignedBy], references: [id])

  @@unique([campaignId, publisherId])
  @@index([campaignId])
  @@index([publisherId])
}
