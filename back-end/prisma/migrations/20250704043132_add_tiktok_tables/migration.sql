-- CreateTable
CREATE TABLE "TiktokAccount" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "tiktokUserId" TEXT NOT NULL,
    "tokens" JSONB NOT NULL,
    "expiry" TIMESTAMP(3) NOT NULL,
    "connectedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TiktokAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TiktokVideo" (
    "id" TEXT NOT NULL,
    "videoId" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "metadata" JSONB NOT NULL,
    "stats" JSONB NOT NULL,
    "fetchedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TiktokVideo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TiktokAccount_tiktokUserId_key" ON "TiktokAccount"("tiktokUserId");

-- CreateIndex
CREATE UNIQUE INDEX "TiktokVideo_videoId_key" ON "TiktokVideo"("videoId");

-- AddForeignKey
ALTER TABLE "TiktokAccount" ADD CONSTRAINT "TiktokAccount_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TiktokVideo" ADD CONSTRAINT "TiktokVideo_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "TiktokAccount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
