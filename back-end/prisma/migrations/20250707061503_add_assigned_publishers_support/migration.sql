-- AlterTable
ALTER TABLE "Campaign" ADD COLUMN     "assignedPublisherIds" TEXT[] DEFAULT ARRAY[]::TEXT[];

-- CreateTable
CREATE TABLE "CampaignPublisherAssignment" (
    "id" TEXT NOT NULL,
    "campaignId" TEXT NOT NULL,
    "publisherId" TEXT NOT NULL,
    "assignedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "CampaignPublisherAssignment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CampaignPublisherAssignment_campaignId_idx" ON "CampaignPublisherAssignment"("campaignId");

-- CreateIndex
CREATE INDEX "CampaignPublisherAssignment_publisherId_idx" ON "CampaignPublisherAssignment"("publisherId");

-- CreateIndex
CREATE UNIQUE INDEX "CampaignPublisherAssignment_campaignId_publisherId_key" ON "CampaignPublisherAssignment"("campaignId", "publisherId");

-- AddForeignKey
ALTER TABLE "CampaignPublisherAssignment" ADD CONSTRAINT "CampaignPublisherAssignment_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "Campaign"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CampaignPublisherAssignment" ADD CONSTRAINT "CampaignPublisherAssignment_publisherId_fkey" FOREIGN KEY ("publisherId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CampaignPublisherAssignment" ADD CONSTRAINT "CampaignPublisherAssignment_assignedBy_fkey" FOREIGN KEY ("assignedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
