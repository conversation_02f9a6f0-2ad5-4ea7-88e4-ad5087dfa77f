# Pay Per Post Backend Architecture

## 1. Overview
This document describes the backend architecture for the Pay Per Post MVP, following an API-first, OpenAPI-driven approach.

## 2. Main Modules/Services
- **Auth Service:** Handles login, JWT, and role-based access.
- **User Service:** CRUD for users, role assignment.
- **Role Service:** CRUD for roles.
- **Campaign Service:** CRUD for campaigns, owned by advertisers.
- **Application Service:** Publishers apply to campaigns, SP Team/Advertiser review.
- **VideoSubmission Service:** Publishers submit TikTok links, SP Team/Advertiser review, feedback/status.
- **Notification Service:** Sends email notifications on status changes.

## 3. Data Flow for TikTok Video Submission
1. Publisher applies to a campaign.
2. Publisher submits a TikTok video link for their application.
3. SP Team reviews the TikTok link, provides feedback/status.
4. Advertiser reviews the TikTok link, provides feedback/status.
5. Notifications are sent to the publisher on each status change.

## 4. Architecture Diagram (Mermaid)
```mermaid
flowchart TD
  subgraph Publisher
    A1[Apply to Campaign]
    A2[Submit TikTok Link]
  end
  subgraph Backend
    B1[Application Service]
    B2[VideoSubmission Service]
    B3[SP Team Review]
    B4[Advertiser Review]
    B5[Notification Service]
  end
  subgraph Frontend
    F1[Campaign List/Detail]
    F2[Application Status]
    F3[TikTok Link Submission UI]
    F4[Review UI]
  end
  A1 --> B1
  A2 --> B2
  B2 --> B3
  B3 --> B4
  B4 --> B2
  B2 --> B5
  B5 --> F2
  F1 --> A1
  F2 --> A2
  F3 --> A2
  F4 --> B3
  F4 --> B4
```

## 5. Notes
- All business logic is encapsulated in services, with controllers handling HTTP requests.
- The TikTok video flow is fully API-driven: publishers submit links, reviewers update status/feedback, and notifications are triggered on status changes.
- The frontend consumes these APIs for all user interactions. 