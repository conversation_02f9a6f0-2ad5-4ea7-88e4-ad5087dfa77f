import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import * as jsonwebtokenLib from 'jsonwebtoken';

describe('AuthService', () => {
  let service: AuthService;
  let userService: Partial<UserService>;
  let jwtService: Partial<JwtService>;

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    passwordHash: bcrypt.hashSync('password123', 10),
    roleId: 'role1',
    name: 'Test User',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    userService = {
      findByEmail: jest.fn(email => Promise.resolve(email === mockUser.email ? mockUser : null)),
      getRoleById: jest.fn(roleId => Promise.resolve({ id: roleId, name: 'Publisher', description: 'Test role' })),
    };
    jwtService = {
      sign: jest.fn((payload: any) => jsonwebtokenLib.sign(typeof payload === 'string' ? JSON.parse(payload) : payload, 'test_secret')),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: UserService, useValue: userService },
        { provide: JwtService, useValue: jwtService },
      ],
    }).compile();
    service = module.get<AuthService>(AuthService);
  });

  it('should return JWT token for valid credentials', async () => {
    const result = await service.login({ email: mockUser.email, password: 'password123' });
    expect(result).toHaveProperty('accessToken');
    expect(typeof result.accessToken).toBe('string');
  });

  it('should return null for wrong password', async () => {
    const result = await service.login({ email: mockUser.email, password: 'wrongpass' });
    expect(result).toBeNull();
  });

  it('should return null for non-existent user', async () => {
    const result = await service.login({ email: '<EMAIL>', password: 'password123' });
    expect(result).toBeNull();
  });

  it('should return JWT token with correct role name in payload', async () => {
    const jwt = require('jsonwebtoken');
    const result = await service.login({ email: mockUser.email, password: 'password123' });
    expect(result).not.toBeNull();
    expect(result.accessToken).toBeDefined();
    const decoded = jwt.decode(result.accessToken);
    expect(decoded).not.toBeNull();
    expect(decoded.role).toBe('publisher');
  });
}); 