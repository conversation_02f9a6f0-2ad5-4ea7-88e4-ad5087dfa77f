import { Injectable } from '@nestjs/common';
import { UserService } from '../user/user.service';
import * as bcrypt from 'bcrypt';
import { LoginDto } from './dto/login.dto';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto) {
    const user = await this.userService.findByEmail(loginDto.email);
    if (!user) return null;
    const isMatch = await bcrypt.compare(loginDto.password, user.passwordHash);
    if (!isMatch) return null;
    
    // Get role name from roleId
    const role = await this.userService.getRoleById(user.roleId);
    const roleName = role ? role.name.toLowerCase().replace(' ', '_') : 'unknown';
    
    // Sign JWT token with user info
    const payload = { sub: user.id, email: user.email, role: roleName };
    const accessToken = this.jwtService.sign(payload);
    return { accessToken };
  }
} 