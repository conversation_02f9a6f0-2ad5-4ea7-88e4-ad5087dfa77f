import { TiktokService } from './tiktok.service';

describe('TiktokService', () => {
  let service: TiktokService;

  beforeEach(() => {
    service = new TiktokService();
  });

  it('should have generateOAuthUrl method', () => {
    expect(typeof service.generateOAuthUrl).toBe('function');
  });

  it('should have exchangeCodeForToken method', () => {
    expect(typeof service.exchangeCodeForToken).toBe('function');
  });

  it('should have getUserInfo method', () => {
    expect(typeof service.getUserInfo).toBe('function');
  });

  it('should have getVideos method', () => {
    expect(typeof service.getVideos).toBe('function');
  });
});