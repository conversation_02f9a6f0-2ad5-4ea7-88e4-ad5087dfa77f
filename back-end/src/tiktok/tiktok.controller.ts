import { Controller, Get, Req, UseGuards, Query, Res } from '@nestjs/common';
import { TiktokService } from './tiktok.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';

@ApiTags('TikTok')
@Controller('tiktok')
export class TiktokController {
  constructor(private readonly tiktokService: TiktokService) {}

  @Get('connect-url')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get TikTok OAuth connect URL for publisher' })
  @ApiResponse({ status: 200, description: 'TikTok OAuth URL returned', schema: { example: { url: 'https://www.tiktok.com/v2/auth/authorize/?...' } } })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('publisher')
  async getConnectUrl(@Req() req) {
    const url = await this.tiktokService.generateOAuthUrl(req.user.id);
    return { url };
  }

  @Get('oauth/callback')
  @ApiOperation({ summary: 'Handle TikTok OAuth callback and exchange code for access token' })
  @ApiResponse({ status: 200, description: 'OAuth callback handled, token stored' })
  async handleOAuthCallback(@Query('code') code: string, @Query('state') state: string, @Res() res: Response) {
    if (!code) {
      return res.status(400).json({ error: 'Missing code' });
    }
    try {
      const result = await this.tiktokService.exchangeCodeForToken(code, state);
      // Redirect or respond as needed
      return res.status(200).json(result);
    } catch (err) {
      return res.status(500).json({ error: err.message });
    }
  }

  @Get('user-info')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get TikTok user info for the current publisher' })
  @ApiResponse({ status: 200, description: 'TikTok user info returned' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('publisher')
  async getUserInfo(@Req() req) {
    const userId = req.user.id;
    return this.tiktokService.getUserInfo(userId);
  }

  @Get('videos')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get TikTok videos for the current publisher' })
  @ApiResponse({ status: 200, description: 'TikTok videos returned' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('publisher')
  async getVideos(@Req() req) {
    const userId = req.user.id;
    return this.tiktokService.getVideos(userId);
  }
} 