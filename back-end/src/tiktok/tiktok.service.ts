import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import fetch from 'node-fetch';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class TiktokService {
  private prisma = new PrismaClient();

  async generateOAuthUrl(userId: string): Promise<string> {
    const clientId = process.env.TIKTOK_CLIENT_ID;
    const redirectUri = encodeURIComponent(process.env.TIKTOK_REDIRECT_URI);
    const scope = encodeURIComponent('user.info.basic video.list');
    const state = crypto.randomBytes(16).toString('hex'); // Should be stored for CSRF protection
    const responseType = 'code';

    // Optionally: Store state in cache/session for later validation

    const url = `https://www.tiktok.com/v2/auth/authorize/?client_key=${clientId}&scope=${scope}&response_type=${responseType}&redirect_uri=${redirectUri}&state=${state}`;
    return url;
  }

  async exchangeCodeForToken(code: string, state: string): Promise<any> {
    const clientId = process.env.TIKTOK_CLIENT_ID;
    const clientSecret = process.env.TIKTOK_CLIENT_SECRET;
    const redirectUri = process.env.TIKTOK_REDIRECT_URI;
    const tokenUrl = 'https://open.tiktokapis.com/v2/oauth/token/';

    const params = new URLSearchParams();
    params.append('client_key', clientId);
    params.append('client_secret', clientSecret);
    params.append('code', code);
    params.append('grant_type', 'authorization_code');
    params.append('redirect_uri', redirectUri);

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params.toString(),
    });
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || 'Failed to exchange code for token');
    }
    // Store access_token and refresh_token for the user (by state or userId)
    const { access_token, refresh_token, expires_in, open_id } = data.data || {};
    if (!access_token || !open_id) {
      throw new Error('Missing access_token or open_id in TikTok response');
    }
    // Assume state is userId (in production, validate state)
    const userId = state;
    const expiry = new Date(Date.now() + (expires_in * 1000));
    // Upsert TiktokAccount
    await this.prisma.tiktokAccount.upsert({
      where: { tiktokUserId: open_id },
      update: {
        tokens: { access_token, refresh_token },
        expiry,
        userId,
      },
      create: {
        tiktokUserId: open_id,
        tokens: { access_token, refresh_token },
        expiry,
        userId,
      },
    });
    return { success: true, tiktokUserId: open_id };
  }

  async getUserInfo(userId: string): Promise<any> {
    // Find the user's TikTok account
    const account = await this.prisma.tiktokAccount.findFirst({ where: { userId } });
    if (!account) {
      throw new Error('No TikTok account connected');
    }
    const { access_token } = account.tokens as { access_token: string; refresh_token: string };
    // Fetch user info from TikTok
    const response = await fetch('https://open.tiktokapis.com/v2/user/info/', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json',
      },
    });
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch TikTok user info');
    }
    return data;
  }

  async getVideos(userId: string): Promise<any> {
    // Find the user's TikTok account
    const account = await this.prisma.tiktokAccount.findFirst({ where: { userId } });
    if (!account) {
      throw new Error('No TikTok account connected');
    }
    const { access_token } = account.tokens as { access_token: string; refresh_token: string };
    // Fetch videos from TikTok
    const response = await fetch('https://open.tiktokapis.com/v2/video/list/', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json',
      },
    });
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.message || 'Failed to fetch TikTok videos');
    }
    return data;
  }
} 