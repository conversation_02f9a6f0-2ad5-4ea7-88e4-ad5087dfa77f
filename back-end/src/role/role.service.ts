import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../notification/prisma.service';
import { CreateRoleDto } from './create-role.dto';
import { UpdateRoleDto } from './update-role.dto';
import { RoleDto } from './role.dto';

@Injectable()
export class RoleService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateRoleDto): Promise<RoleDto> {
    try {
      // Check if role name already exists
      const existingRole = await this.prisma.role.findUnique({
        where: { name: data.name },
      });

      if (existingRole) {
        throw new ConflictException(`Role with name '${data.name}' already exists`);
      }

      return this.prisma.role.create({
        data: {
          id: data.name, // Use name as ID for predefined roles
          name: data.name,
          description: data.description || null,
          permissions: data.permissions || [],
        },
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new ConflictException('Failed to create role');
    }
  }

  async findAll(): Promise<RoleDto[]> {
    return this.prisma.role.findMany({
      orderBy: { name: 'asc' },
    });
  }

  async findOne(id: string): Promise<RoleDto | null> {
    const role = await this.prisma.role.findUnique({ where: { id } });

    if (!role) {
      throw new NotFoundException(`Role with ID '${id}' not found`);
    }

    return role;
  }

  async update(id: string, data: UpdateRoleDto): Promise<RoleDto> {
    try {
      // Check if role exists
      await this.findOne(id);

      // If updating name, check for conflicts
      if (data.name && data.name !== id) {
        const existingRole = await this.prisma.role.findUnique({
          where: { name: data.name },
        });

        if (existingRole) {
          throw new ConflictException(`Role with name '${data.name}' already exists`);
        }
      }

      return this.prisma.role.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          permissions: data.permissions,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new ConflictException('Failed to update role');
    }
  }

  async remove(id: string): Promise<RoleDto> {
    try {
      // Check if role exists
      await this.findOne(id);

      // Check if role is being used by any users
      const usersWithRole = await this.prisma.user.findMany({
        where: { roleId: id },
        select: { id: true },
      });

      if (usersWithRole.length > 0) {
        throw new ConflictException(
          `Cannot delete role '${id}' as it is assigned to ${usersWithRole.length} user(s)`
        );
      }

      return this.prisma.role.delete({ where: { id } });
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new ConflictException('Failed to delete role');
    }
  }

  async getRoleUsers(id: string): Promise<any[]> {
    // Check if role exists
    await this.findOne(id);

    return this.prisma.user.findMany({
      where: { roleId: id },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
      },
      orderBy: { name: 'asc' },
    });
  }
}
