import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { RoleService } from './role.service';
import { Roles } from '../auth/roles.decorator';
import { AuthGuard } from '@nestjs/passport';
import { CreateRoleDto } from './create-role.dto';
import { UpdateRoleDto } from './update-role.dto';
import { RoleDto } from './role.dto';

@UseGuards(AuthGuard('jwt'))
@ApiTags('Role')
@ApiBearerAuth()
@Controller('role')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @Roles('admin')
  @ApiOperation({
    summary: 'Create a new role',
    description: 'Create a new role with specified permissions. Only admin users can create roles.'
  })
  @ApiResponse({
    status: 201,
    description: 'Role created successfully',
    type: RoleDto
  })
  @ApiResponse({ status: 409, description: 'Role name already exists' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.roleService.create(createRoleDto);
  }

  @Get()
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Get all roles',
    description: 'Retrieve a list of all roles in the system.'
  })
  @ApiResponse({
    status: 200,
    description: 'List of roles retrieved successfully',
    type: [RoleDto]
  })
  findAll() {
    return this.roleService.findAll();
  }

  @Get(':id')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Get role by ID',
    description: 'Retrieve a specific role by its ID.'
  })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({
    status: 200,
    description: 'Role found',
    type: RoleDto
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  findOne(@Param('id') id: string) {
    return this.roleService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({
    summary: 'Update role by ID',
    description: 'Update an existing role. Only admin users can update roles.'
  })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({
    status: 200,
    description: 'Role updated successfully',
    type: RoleDto
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  @ApiResponse({ status: 409, description: 'Role name already exists' })
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.roleService.update(id, updateRoleDto);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({
    summary: 'Delete role by ID',
    description: 'Delete a role. Cannot delete roles that are assigned to users.'
  })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({
    status: 200,
    description: 'Role deleted successfully',
    type: RoleDto
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  @ApiResponse({ status: 409, description: 'Role is assigned to users and cannot be deleted' })
  remove(@Param('id') id: string) {
    return this.roleService.remove(id);
  }

  @Get(':id/users')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Get users assigned to a role',
    description: 'Retrieve a list of users assigned to a specific role.'
  })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({
    status: 200,
    description: 'List of users with this role',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          email: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  getRoleUsers(@Param('id') id: string) {
    return this.roleService.getRoleUsers(id);
  }
}
