import { Is<PERSON><PERSON>, IsNotEmpty, IsArray, IsOptional, ArrayUnique } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateRoleDto {
  @ApiProperty({
    description: 'Role name (must be unique)',
    example: 'content_moderator',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Content moderation and review role',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Array of permissions for this role',
    example: ['read:campaigns', 'write:reviews', 'moderate:content'],
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayUnique()
  @IsOptional()
  permissions?: string[];
}
