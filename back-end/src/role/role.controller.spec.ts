import { Test, TestingModule } from '@nestjs/testing';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';
import { CreateRoleDto } from './create-role.dto';
import { UpdateRoleDto } from './update-role.dto';

describe('RoleController', () => {
  let controller: RoleController;
  let service: RoleService;

  const mockRoleService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getRoleUsers: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleController],
      providers: [
        { provide: RoleService, useValue: mockRoleService },
      ],
    }).compile();

    controller = module.get<RoleController>(RoleController);
    service = module.get<RoleService>(RoleService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a role', async () => {
    const dto: CreateRoleDto = { name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    const role = { id: '1', ...dto };
    mockRoleService.create.mockResolvedValue(role);
    await expect(controller.create(dto)).resolves.toEqual(role);
  });

  it('should return all roles', async () => {
    const roles = [{ id: '1', name: 'admin' }];
    mockRoleService.findAll.mockResolvedValue(roles);
    await expect(controller.findAll()).resolves.toEqual(roles);
  });

  it('should return one role', async () => {
    const role = { id: '1', name: 'admin' };
    mockRoleService.findOne.mockResolvedValue(role);
    await expect(controller.findOne('1')).resolves.toEqual(role);
  });

  it('should update a role', async () => {
    const updateDto: UpdateRoleDto = { name: 'user', description: 'User', permissions: ['read'] };
    const role = { id: '1', ...updateDto };
    mockRoleService.update.mockResolvedValue(role);
    await expect(controller.update('1', updateDto)).resolves.toEqual(role);
    expect(mockRoleService.update).toHaveBeenCalledWith('1', updateDto);
  });

  it('should remove a role', async () => {
    const role = { id: '1', name: 'admin' };
    mockRoleService.remove.mockResolvedValue(role);
    await expect(controller.remove('1')).resolves.toEqual(role);
    expect(mockRoleService.remove).toHaveBeenCalledWith('1');
  });

  it('should get role users', async () => {
    const users = [
      { id: 'user1', name: 'User 1', email: '<EMAIL>', createdAt: new Date() },
      { id: 'user2', name: 'User 2', email: '<EMAIL>', createdAt: new Date() },
    ];
    mockRoleService.getRoleUsers.mockResolvedValue(users);
    await expect(controller.getRoleUsers('1')).resolves.toEqual(users);
    expect(mockRoleService.getRoleUsers).toHaveBeenCalledWith('1');
  });
});
