import { ApiProperty } from '@nestjs/swagger';

export class RoleDto {
  @ApiProperty({
    description: 'Unique role identifier',
    example: 'role_uuid_123',
  })
  id: string;

  @ApiProperty({
    description: 'Role name',
    example: 'content_moderator',
  })
  name: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Content moderation and review role',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Array of permissions for this role',
    example: ['read:campaigns', 'write:reviews', 'moderate:content'],
    type: [String],
  })
  permissions: string[];
}
