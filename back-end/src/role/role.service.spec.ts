import { Test, TestingModule } from '@nestjs/testing';
import { RoleService } from './role.service';
import { CreateRoleDto } from './create-role.dto';
import { PrismaService } from '../notification/prisma.service';
import { ConflictException, NotFoundException } from '@nestjs/common';

const mockPrismaService = {
  role: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  user: {
    findMany: jest.fn(),
  },
};

describe('RoleService', () => {
  let service: RoleService;
  let prisma: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleService,
        { provide: PrismaService, useValue: mockPrismaService },
      ],
    }).compile();
    service = module.get<RoleService>(RoleService);
    prisma = mockPrismaService;

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a role', async () => {
    const role = { id: 'admin', name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    const createDto: CreateRoleDto = { name: 'admin', description: 'Administrator', permissions: ['manage_users'] };

    prisma.role.findUnique.mockResolvedValue(null); // No existing role
    prisma.role.create.mockResolvedValue(role);

    await expect(service.create(createDto)).resolves.toEqual(role);
    expect(prisma.role.create).toHaveBeenCalledWith({
      data: {
        id: 'admin',
        name: 'admin',
        description: 'Administrator',
        permissions: ['manage_users'],
      },
    });
  });

  it('should throw ConflictException if role name already exists', async () => {
    const createDto: CreateRoleDto = { name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    const existingRole = { id: 'admin', name: 'admin', description: 'Existing', permissions: [] };

    prisma.role.findUnique.mockResolvedValue(existingRole);

    await expect(service.create(createDto)).rejects.toThrow(ConflictException);
  });

  it('should find all roles', async () => {
    const roles = [{ id: '1', name: 'admin' }];
    prisma.role.findMany.mockResolvedValue(roles);
    await expect(service.findAll()).resolves.toEqual(roles);
    expect(prisma.role.findMany).toHaveBeenCalledWith({
      orderBy: { name: 'asc' },
    });
  });

  it('should find one role', async () => {
    const role = { id: '1', name: 'admin' };
    prisma.role.findUnique.mockResolvedValue(role);
    await expect(service.findOne('1')).resolves.toEqual(role);
  });

  it('should throw NotFoundException if role not found', async () => {
    prisma.role.findUnique.mockResolvedValue(null);
    await expect(service.findOne('invalid')).rejects.toThrow(NotFoundException);
  });

  it('should update a role', async () => {
    const existingRole = { id: '1', name: 'admin' };
    const updatedRole = { id: '1', name: 'user' };

    prisma.role.findUnique
      .mockResolvedValueOnce(existingRole) // First call for existence check
      .mockResolvedValueOnce(null); // Second call for name conflict check
    prisma.role.update.mockResolvedValue(updatedRole);

    await expect(service.update('1', { name: 'user' })).resolves.toEqual(updatedRole);
  });

  it('should remove a role', async () => {
    const role = { id: '1', name: 'admin' };

    prisma.role.findUnique.mockResolvedValue(role);
    prisma.user.findMany.mockResolvedValue([]); // No users with this role
    prisma.role.delete.mockResolvedValue(role);

    await expect(service.remove('1')).resolves.toEqual(role);
  });

  it('should throw ConflictException when trying to delete role assigned to users', async () => {
    const role = { id: '1', name: 'admin' };
    const users = [{ id: 'user1' }];

    prisma.role.findUnique.mockResolvedValue(role);
    prisma.user.findMany.mockResolvedValue(users);

    await expect(service.remove('1')).rejects.toThrow(ConflictException);
  });

  it('should get role users', async () => {
    const role = { id: '1', name: 'admin' };
    const users = [{ id: 'user1', name: 'User 1', email: '<EMAIL>', createdAt: new Date() }];

    prisma.role.findUnique.mockResolvedValue(role);
    prisma.user.findMany.mockResolvedValue(users);

    await expect(service.getRoleUsers('1')).resolves.toEqual(users);
  });
});
