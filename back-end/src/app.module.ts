import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { RoleModule } from './role/role.module';
import { CampaignModule } from './campaign/campaign.module';
import { AuthModule } from './auth/auth.module';
import { ApplicationModule } from './application/application.module';
import { VideoSubmissionModule } from './video-submission/video-submission.module';
import { TiktokModule } from './tiktok/tiktok.module';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './auth/roles.guard';

@Module({
  imports: [UserModule, RoleModule, CampaignModule, AuthModule, ApplicationModule, VideoSubmissionModule, TiktokModule],
  controllers: [AppController],
  providers: [AppService, { provide: APP_GUARD, useClass: RolesGuard }],
})
export class AppModule {}
