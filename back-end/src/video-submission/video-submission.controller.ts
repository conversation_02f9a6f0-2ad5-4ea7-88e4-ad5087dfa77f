import { <PERSON>, Post, Body, <PERSON>, Param, Get, Query } from '@nestjs/common';
import { VideoSubmissionService } from './video-submission.service';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { UpdateVideoSubmissionStatusDto } from './update-status.dto';

@ApiTags('Video Submission')
@Controller('video-submission')
export class VideoSubmissionController {
  constructor(private readonly videoSubmissionService: VideoSubmissionService) {}

  @Post('submit')
  @ApiOperation({ summary: 'Submit a new video' })
  @ApiBody({ description: 'Video submission data', type: Object })
  @ApiResponse({ status: 201, description: 'Video submitted successfully' })
  submitVideo(@Body() data: any) {
    return this.videoSubmissionService.submitVideo(data);
  }

  @Patch('review/:id')
  @ApiOperation({ summary: 'Review a video submission' })
  @ApiParam({ name: 'id', description: 'ID of the video submission' })
  @ApiBody({ description: 'Review data', type: Object })
  @ApiResponse({ status: 200, description: 'Video reviewed successfully' })
  reviewVideo(@Param('id') id: string, @Body() data: any) {
    return this.videoSubmissionService.reviewVideo(id, data);
  }

  @Patch('feedback/:id')
  @ApiOperation({ summary: 'Give feedback on a video submission' })
  @ApiParam({ name: 'id', description: 'ID of the video submission' })
  @ApiBody({ description: 'Feedback data', type: Object })
  @ApiResponse({ status: 200, description: 'Feedback given successfully' })
  giveFeedback(@Param('id') id: string, @Body() data: any) {
    return this.videoSubmissionService.giveFeedback(id, data);
  }

  @Get()
  @ApiOperation({ summary: 'Get video submissions by status' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter submissions by status' })
  @ApiResponse({ status: 200, description: 'List of video submissions' })
  getSubmissions(@Query('status') status?: string) {
    return this.videoSubmissionService.listSubmissionsByStatus(status);
  }

  @Get(':id/status-history')
  @ApiOperation({ summary: 'Get status history for a video submission' })
  @ApiParam({ name: 'id', description: 'ID of the video submission' })
  @ApiResponse({ status: 200, description: 'Status history returned' })
  getStatusHistory(@Param('id') id: string) {
    return this.videoSubmissionService.getStatusHistory(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update the status of a video submission' })
  @ApiParam({ name: 'id', description: 'ID of the video submission' })
  @ApiBody({ type: UpdateVideoSubmissionStatusDto, description: 'Status update data' })
  @ApiResponse({ status: 200, description: 'Status updated successfully' })
  updateStatus(
    @Param('id') id: string,
    @Body() dto: UpdateVideoSubmissionStatusDto,
  ) {
    return this.videoSubmissionService.updateStatus(id, dto);
  }
}