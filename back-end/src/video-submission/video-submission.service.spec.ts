import { Test, TestingModule } from '@nestjs/testing';
import { VideoSubmissionService } from './video-submission.service';

describe('VideoSubmissionService', () => {
  let service: VideoSubmissionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [VideoSubmissionService],
    }).compile();

    service = module.get<VideoSubmissionService>(VideoSubmissionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have submitVideo method', () => {
    expect(typeof service.submitVideo).toBe('function');
  });

  it('should have reviewVideo method', () => {
    expect(typeof service.reviewVideo).toBe('function');
  });

  it('should have giveFeedback method', () => {
    expect(typeof service.giveFeedback).toBe('function');
  });

  it('should call submitVideo without error', () => {
    expect(() => service.submitVideo({ tiktokLink: 'link' })).not.toThrow();
  });

  it('should call reviewVideo without error', () => {
    expect(() => service.reviewVideo('id', { status: 'approved' },)).not.toThrow();
  });

  it('should call giveFeedback without error', () => {
    expect(() => service.giveFeedback('id', { feedback: 'ok' })).not.toThrow();
  });
});
