import { Test, TestingModule } from '@nestjs/testing';
import { VideoSubmissionController } from './video-submission.controller';
import { VideoSubmissionService } from './video-submission.service';

describe('VideoSubmissionController', () => {
  let controller: VideoSubmissionController;
  let service: VideoSubmissionService;

  const mockService = {
    submitVideo: jest.fn(),
    reviewVideo: jest.fn(),
    giveFeedback: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VideoSubmissionController],
      providers: [{ provide: VideoSubmissionService, useValue: mockService }],
    }).compile();

    controller = module.get<VideoSubmissionController>(VideoSubmissionController);
    service = module.get<VideoSubmissionService>(VideoSubmissionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service.submitVideo with correct data', () => {
    const data = { tiktokLink: 'link' };
    controller.submitVideo(data);
    expect(service.submitVideo).toHaveBeenCalledWith(data);
  });

  it('should call service.reviewVideo with correct id and data', () => {
    const id = '123';
    const data = { status: 'approved' };
    controller.reviewVideo(id, data);
    expect(service.reviewVideo).toHaveBeenCalledWith(id, data);
  });

  it('should call service.giveFeedback with correct id and data', () => {
    const id = '456';
    const data = { feedback: 'Good job' };
    controller.giveFeedback(id, data);
    expect(service.giveFeedback).toHaveBeenCalledWith(id, data);
  });
});
