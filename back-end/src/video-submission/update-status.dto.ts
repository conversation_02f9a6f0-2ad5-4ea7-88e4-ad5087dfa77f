import { IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateVideoSubmissionStatusDto {
  @ApiProperty({
    description: 'New status for the video submission',
    example: 'approved',
  })
  @IsString()
  status: string;

  @ApiPropertyOptional({
    description: 'Reason for status change, if applicable',
    example: 'Video quality meets requirements',
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Additional comments regarding the status update',
    example: 'Reviewed by admin',
  })
  @IsString()
  @IsOptional()
  comment?: string;
}