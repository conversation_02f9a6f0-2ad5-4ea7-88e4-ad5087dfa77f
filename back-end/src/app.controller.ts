import { Controller, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { PrismaClient } from '@prisma/client';

@ApiTags('System')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  async getStatus(@Res() res: Response) {
    const prisma = new PrismaClient();
    const uptime = process.uptime();
    const uptimeStr = `${Math.floor(uptime / 60)}m ${Math.floor(uptime % 60)}s`;
    const now = new Date().toLocaleString();
    let dbStatus = 'Unknown';
    try {
      await prisma.$queryRaw`SELECT 1`;
      dbStatus = 'Connected';
    } catch {
      dbStatus = 'Error';
    }
    const commitHash = '6bc92fe';
    res.setHeader('Content-Type', 'text/html');
    res.send(`
      <html>
        <head>
          <title>API Status</title>
          <style>
            body { font-family: sans-serif; background: #f9f9f9; color: #222; margin: 0; padding: 0; }
            .container { max-width: 480px; margin: 60px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 8px #0001; padding: 32px; text-align: center; }
            h1 { color: #2563eb; }
            .status { font-size: 1.2em; margin: 16px 0; color: #16a34a; }
            .info { color: #555; font-size: 0.95em; }
            .meta { color: #888; font-size: 0.9em; margin-top: 12px; }
            .logo { width: 80px; margin-bottom: 16px; }
            .db-status { color: ${dbStatus === 'Connected' ? '#16a34a' : '#dc2626'}; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🚀 Pay Per Post API</h1>
            <div class="status">Status: <b>Online</b></div>
            <div class="info">Version: 1.0<br/>Environment: ${process.env.NODE_ENV || 'development'}</div>
            <div class="meta">Uptime: ${uptimeStr}<br/>Server Time: ${now}</div>
            <div class="meta">Database: <span class="db-status">${dbStatus}</span></div>
            <div class="meta">Commit: <code>${commitHash}</code></div>
            <div style="margin-top:24px;">
              <a href="/api" style="color:#2563eb;text-decoration:underline;">View API Docs (Swagger)</a>
            </div>
          </div>
        </body>
      </html>
    `);
  }
}
