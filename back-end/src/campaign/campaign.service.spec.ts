import { Test, TestingModule } from '@nestjs/testing';
import { CampaignService } from './campaign.service';
import { PrismaService } from '../notification/prisma.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';

const mockPrismaService = {
  campaign: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  user: {
    findMany: jest.fn(),
  },
  campaignPublisherAssignment: {
    upsert: jest.fn(),
    updateMany: jest.fn(),
    findMany: jest.fn(),
  },
};

describe('CampaignService', () => {
  let service: CampaignService;
  let prisma: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignService,
        { provide: PrismaService, useValue: mockPrismaService },
      ],
    }).compile();
    service = module.get<CampaignService>(CampaignService);
    prisma = mockPrismaService;

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a campaign', async () => {
    const campaignData = {
      title: 'A',
      description: 'B',
      advertiserId: 'u',
      startDate: new Date(),
      endDate: new Date(),
    };
    const campaign = {
      id: '1',
      ...campaignData,
      status: 'draft',
      assignedPublisherIds: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    prisma.campaign.create.mockResolvedValue(campaign);
    await expect(service.create(campaignData)).resolves.toEqual(campaign);
    expect(prisma.campaign.create).toHaveBeenCalledWith({
      data: { ...campaignData, status: 'draft' },
    });
  });

  it('should find all campaigns', async () => {
    const campaigns = [{ id: '1' }];
    prisma.campaign.findMany.mockResolvedValue(campaigns);
    await expect(service.findAll()).resolves.toEqual(campaigns);
  });

  it('should find one campaign', async () => {
    const campaign = { id: '1' };
    prisma.campaign.findUnique.mockResolvedValue(campaign);
    await expect(service.findOne('1')).resolves.toEqual(campaign);
  });

  it('should update a campaign', async () => {
    const campaign = { id: '1', title: 'B' };
    prisma.campaign.update.mockResolvedValue(campaign);
    await expect(service.update('1', { title: 'B' })).resolves.toEqual(campaign);
  });

  it('should remove a campaign', async () => {
    const campaign = { id: '1' };
    prisma.campaign.delete.mockResolvedValue(campaign);
    await expect(service.remove('1')).resolves.toEqual(campaign);
  });

  describe('Role-based Access', () => {
    it('should return all campaigns for admin', async () => {
      const campaigns = [{ id: '1' }, { id: '2' }];
      prisma.campaign.findMany.mockResolvedValue(campaigns);

      const result = await service.findAllForUser('admin1', 'admin');
      expect(result).toEqual(campaigns);
      expect(prisma.campaign.findMany).toHaveBeenCalledWith({
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    });

    it('should return only own campaigns for advertiser', async () => {
      const campaigns = [{ id: '1', advertiserId: 'adv1' }];
      prisma.campaign.findMany.mockResolvedValue(campaigns);

      const result = await service.findAllForUser('adv1', 'advertiser');
      expect(result).toEqual(campaigns);
      expect(prisma.campaign.findMany).toHaveBeenCalledWith({
        where: { advertiserId: 'adv1' },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    });

    it('should return published and assigned campaigns for publisher', async () => {
      const campaigns = [{ id: '1', status: 'published' }];
      prisma.campaign.findMany.mockResolvedValue(campaigns);

      const result = await service.findAllForUser('pub1', 'publisher');
      expect(result).toEqual(campaigns);
      expect(prisma.campaign.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { status: 'published' },
            { assignedPublisherIds: { has: 'pub1' } },
          ],
        },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    });
  });

  describe('Publisher Assignment', () => {
    it('should assign publishers to campaign', async () => {
      const campaign = { id: 'camp1', assignedPublisherIds: [] };
      const publishers = [{ id: 'pub1', role: { name: 'publisher' } }];
      const assignment = {
        id: 'assign1',
        campaignId: 'camp1',
        publisherId: 'pub1',
        assignedBy: 'staff1',
        publisher: { id: 'pub1', name: 'Publisher', email: '<EMAIL>' },
        assigner: { id: 'staff1', name: 'Staff', email: '<EMAIL>' },
      };

      prisma.campaign.findUnique.mockResolvedValue(campaign);
      prisma.user.findMany.mockResolvedValue(publishers);
      prisma.campaignPublisherAssignment.upsert.mockResolvedValue(assignment);
      prisma.campaign.update.mockResolvedValue({ ...campaign, assignedPublisherIds: ['pub1'] });

      const result = await service.assignPublishers('camp1', { publisherIds: ['pub1'] }, 'staff1');
      expect(result).toEqual([assignment]);
    });

    it('should throw NotFoundException if campaign not found', async () => {
      prisma.campaign.findUnique.mockResolvedValue(null);

      await expect(
        service.assignPublishers('invalid', { publisherIds: ['pub1'] }, 'staff1')
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if publisher not found', async () => {
      const campaign = { id: 'camp1', assignedPublisherIds: [] };
      prisma.campaign.findUnique.mockResolvedValue(campaign);
      prisma.user.findMany.mockResolvedValue([]); // No publishers found

      await expect(
        service.assignPublishers('camp1', { publisherIds: ['invalid'] }, 'staff1')
      ).rejects.toThrow(BadRequestException);
    });
  });
});
