import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../notification/prisma.service';
import { AssignPublishersDto, RemovePublishersDto } from './assign-publishers.dto';
import { CampaignPublisherAssignmentDto } from './campaign-publisher-assignment.dto';

type Campaign = {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  startDate: Date;
  endDate: Date;
  status: string;
  assignedPublisherIds: string[];
  createdAt: Date;
  updatedAt: Date;
};

@Injectable()
export class CampaignService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Campaign> {
    // Accept CreateCampaignDto, add status before saving
    const campaignData = {
      ...data,
      status: 'draft', // or 'active' as your business logic requires
    };
    return this.prisma.campaign.create({ data: campaignData });
  }

  async findAll(): Promise<Campaign[]> {
    return this.prisma.campaign.findMany();
  }

  async findOne(id: string): Promise<Campaign | null> {
    return this.prisma.campaign.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<Omit<Campaign, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Campaign> {
    return this.prisma.campaign.update({ where: { id }, data });
  }

  async remove(id: string): Promise<Campaign> {
    return this.prisma.campaign.delete({ where: { id } });
  }

  // Publisher Assignment Methods
  async assignPublishers(
    campaignId: string,
    assignPublishersDto: AssignPublishersDto,
    assignedBy: string,
  ): Promise<CampaignPublisherAssignmentDto[]> {
    // Verify campaign exists
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${campaignId} not found`);
    }

    // Verify all publishers exist and have publisher role
    const publishers = await this.prisma.user.findMany({
      where: {
        id: { in: assignPublishersDto.publisherIds },
        role: { name: 'publisher' },
      },
    });

    if (publishers.length !== assignPublishersDto.publisherIds.length) {
      throw new BadRequestException('One or more publisher IDs are invalid or not publishers');
    }

    // Create assignments (using upsert to handle duplicates)
    const assignments = await Promise.all(
      assignPublishersDto.publisherIds.map(async (publisherId) => {
        return this.prisma.campaignPublisherAssignment.upsert({
          where: {
            campaignId_publisherId: {
              campaignId,
              publisherId,
            },
          },
          update: {
            isActive: true,
            assignedBy,
            assignedAt: new Date(),
          },
          create: {
            campaignId,
            publisherId,
            assignedBy,
          },
          include: {
            publisher: {
              select: { id: true, name: true, email: true },
            },
            assigner: {
              select: { id: true, name: true, email: true },
            },
          },
        });
      }),
    );

    // Update campaign's assignedPublisherIds array
    const currentAssignedIds = campaign.assignedPublisherIds || [];
    const newAssignedIds = Array.from(
      new Set([...currentAssignedIds, ...assignPublishersDto.publisherIds]),
    );

    await this.prisma.campaign.update({
      where: { id: campaignId },
      data: { assignedPublisherIds: newAssignedIds },
    });

    return assignments;
  }

  async removePublishers(
    campaignId: string,
    removePublishersDto: RemovePublishersDto,
  ): Promise<{ removed: number }> {
    // Verify campaign exists
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${campaignId} not found`);
    }

    // Deactivate assignments
    const result = await this.prisma.campaignPublisherAssignment.updateMany({
      where: {
        campaignId,
        publisherId: { in: removePublishersDto.publisherIds },
        isActive: true,
      },
      data: { isActive: false },
    });

    // Update campaign's assignedPublisherIds array
    const currentAssignedIds = campaign.assignedPublisherIds || [];
    const updatedAssignedIds = currentAssignedIds.filter(
      (id) => !removePublishersDto.publisherIds.includes(id),
    );

    await this.prisma.campaign.update({
      where: { id: campaignId },
      data: { assignedPublisherIds: updatedAssignedIds },
    });

    return { removed: result.count };
  }

  async getAssignedPublishers(campaignId: string): Promise<CampaignPublisherAssignmentDto[]> {
    const assignments = await this.prisma.campaignPublisherAssignment.findMany({
      where: {
        campaignId,
        isActive: true,
      },
      include: {
        publisher: {
          select: { id: true, name: true, email: true },
        },
        assigner: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { assignedAt: 'desc' },
    });

    return assignments;
  }

  // Role-based filtering methods
  async findAllForUser(userId: string, userRole: string): Promise<Campaign[]> {
    if (userRole === 'admin' || userRole === 'staff' || userRole === 'spteam') {
      // Admin and staff can see all campaigns
      return this.prisma.campaign.findMany({
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    } else if (userRole === 'advertiser') {
      // Advertisers can only see their own campaigns
      return this.prisma.campaign.findMany({
        where: { advertiserId: userId },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    } else if (userRole === 'publisher') {
      // Publishers can only see published campaigns or campaigns assigned to them
      return this.prisma.campaign.findMany({
        where: {
          OR: [
            { status: 'published' },
            { assignedPublisherIds: { has: userId } },
          ],
        },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    }

    return [];
  }

  async findOneForUser(id: string, userId: string, userRole: string): Promise<Campaign | null> {
    if (userRole === 'admin' || userRole === 'staff' || userRole === 'spteam') {
      // Admin and staff can access any campaign
      return this.prisma.campaign.findUnique({
        where: { id },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
          publisherAssignments: {
            where: { isActive: true },
            include: {
              publisher: {
                select: { id: true, name: true, email: true },
              },
            },
          },
        },
      });
    } else if (userRole === 'advertiser') {
      // Advertisers can only access their own campaigns
      return this.prisma.campaign.findFirst({
        where: { id, advertiserId: userId },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
          publisherAssignments: {
            where: { isActive: true },
            include: {
              publisher: {
                select: { id: true, name: true, email: true },
              },
            },
          },
        },
      });
    } else if (userRole === 'publisher') {
      // Publishers can only access published campaigns or campaigns assigned to them
      return this.prisma.campaign.findFirst({
        where: {
          id,
          OR: [
            { status: 'published' },
            { assignedPublisherIds: { has: userId } },
          ],
        },
        include: {
          advertiser: {
            select: { id: true, name: true, email: true },
          },
        },
      });
    }

    return null;
  }
}
