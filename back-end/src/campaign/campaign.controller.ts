import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  ForbiddenException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { CampaignService } from './campaign.service';
import { Roles } from '../auth/roles.decorator';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CreateCampaignDto } from './create-campaign.dto';
import { AssignPublishersDto, RemovePublishersDto } from './assign-publishers.dto';
import { CampaignPublisherAssignmentDto } from './campaign-publisher-assignment.dto';

@UseGuards(AuthGuard('jwt'))
@ApiTags('Campaign')
@ApiBearerAuth()
@Controller('campaign')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post()
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Create a new campaign',
    description:
      'Only users with admin, staff, or spteam roles are authorized to create campaigns. Requires Bearer JWT authentication.',
  })
  @ApiResponse({
    status: 201,
    description:
      'Campaign created successfully. Only accessible by users with admin, staff, or spteam roles.',
    type: CreateCampaignDto,
  })
  create(@Body() data: CreateCampaignDto) {
    return this.campaignService.create(data);
  }

  @Get()
  @ApiOperation({
    summary: 'Get campaigns based on user role',
    description: 'Returns campaigns filtered by user role: admin/staff see all, advertisers see own, publishers see published/assigned'
  })
  @ApiResponse({ status: 200, description: 'List of campaigns filtered by role' })
  findAll(@Req() req: any) {
    const { id: userId, role } = req.user;
    return this.campaignService.findAllForUser(userId, role);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get campaign by ID with role-based access',
    description: 'Returns campaign details if user has permission to view it'
  })
  @ApiResponse({ status: 200, description: 'Campaign found' })
  @ApiResponse({ status: 403, description: 'Forbidden - insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Campaign not found or no access' })
  async findOne(@Param('id') id: string, @Req() req: any) {
    const { id: userId, role } = req.user;
    const campaign = await this.campaignService.findOneForUser(id, userId, role);

    if (!campaign) {
      throw new ForbiddenException('Campaign not found or you do not have permission to view it');
    }

    return campaign;
  }

  @Patch(':id')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({ summary: 'Update campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign updated' })
  update(@Param('id') id: string, @Body() data: any) {
    return this.campaignService.update(id, data);
  }

  @Delete(':id')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({ summary: 'Delete campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign deleted' })
  remove(@Param('id') id: string) {
    return this.campaignService.remove(id);
  }

  // Publisher Assignment Endpoints
  @Post(':id/assign-publishers')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Assign publishers to a campaign',
    description: 'Assign specific publishers to a campaign. Only admin, staff, and spteam can perform this action.',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({
    status: 201,
    description: 'Publishers assigned successfully',
    type: [CampaignPublisherAssignmentDto],
  })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  @ApiResponse({ status: 400, description: 'Invalid publisher IDs' })
  assignPublishers(
    @Param('id') campaignId: string,
    @Body() assignPublishersDto: AssignPublishersDto,
    @Req() req: any,
  ) {
    const { id: assignedBy } = req.user;
    return this.campaignService.assignPublishers(campaignId, assignPublishersDto, assignedBy);
  }

  @Delete(':id/remove-publishers')
  @Roles('admin', 'staff', 'spteam')
  @ApiOperation({
    summary: 'Remove publishers from a campaign',
    description: 'Remove specific publishers from a campaign. Only admin, staff, and spteam can perform this action.',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({
    status: 200,
    description: 'Publishers removed successfully',
    schema: {
      type: 'object',
      properties: {
        removed: { type: 'number', description: 'Number of publishers removed' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  removePublishers(
    @Param('id') campaignId: string,
    @Body() removePublishersDto: RemovePublishersDto,
  ) {
    return this.campaignService.removePublishers(campaignId, removePublishersDto);
  }

  @Get(':id/assigned-publishers')
  @Roles('admin', 'staff', 'spteam', 'advertiser')
  @ApiOperation({
    summary: 'Get assigned publishers for a campaign',
    description: 'Retrieve list of publishers assigned to a campaign.',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({
    status: 200,
    description: 'List of assigned publishers',
    type: [CampaignPublisherAssignmentDto],
  })
  @ApiResponse({ status: 404, description: 'Campaign not found' })
  getAssignedPublishers(@Param('id') campaignId: string) {
    return this.campaignService.getAssignedPublishers(campaignId);
  }
}
