import { ApiProperty } from '@nestjs/swagger';

export class CampaignPublisherAssignmentDto {
  @ApiProperty({
    description: 'Unique identifier for the assignment',
    example: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Campaign ID',
    example: 'uuid',
  })
  campaignId: string;

  @ApiProperty({
    description: 'Publisher ID',
    example: 'uuid',
  })
  publisherId: string;

  @ApiProperty({
    description: 'When the publisher was assigned',
    example: '2025-07-07T06:15:00Z',
  })
  assignedAt: Date;

  @ApiProperty({
    description: 'ID of the user who made the assignment',
    example: 'uuid',
  })
  assignedBy: string;

  @ApiProperty({
    description: 'Whether the assignment is currently active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Publisher information',
    required: false,
  })
  publisher?: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({
    description: 'Assigner information',
    required: false,
  })
  assigner?: {
    id: string;
    name: string;
    email: string;
  };
}
