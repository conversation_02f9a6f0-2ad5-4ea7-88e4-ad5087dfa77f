import { Is<PERSON>rray, IsUUID, ArrayNotEmpty, ArrayUnique } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AssignPublishersDto {
  @ApiProperty({
    description: 'Array of publisher IDs to assign to the campaign',
    example: ['uuid1', 'uuid2', 'uuid3'],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsUUID('4', { each: true })
  publisherIds: string[];
}

export class RemovePublishersDto {
  @ApiProperty({
    description: 'Array of publisher IDs to remove from the campaign',
    example: ['uuid1', 'uuid2'],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsUUID('4', { each: true })
  publisherIds: string[];
}
