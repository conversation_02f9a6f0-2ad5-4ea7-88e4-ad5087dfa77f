import { Test, TestingModule } from '@nestjs/testing';
import { CampaignController } from './campaign.controller';
import { CampaignService } from './campaign.service';
import { ForbiddenException } from '@nestjs/common';

describe('CampaignController', () => {
  let controller: CampaignController;

  const mockCampaignService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findAllForUser: jest.fn(),
    findOne: jest.fn(),
    findOneForUser: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    assignPublishers: jest.fn(),
    removePublishers: jest.fn(),
    getAssignedPublishers: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CampaignController],
      providers: [{ provide: CampaignService, useValue: mockCampaignService }],
    }).compile();

    controller = module.get<CampaignController>(CampaignController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a campaign', async () => {
    const dto = {
      title: 'A',
      description: 'B',
      advertiserId: 'u',
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
    };
    const campaign = { id: '1', ...dto, status: 'draft' };
    mockCampaignService.create.mockResolvedValue(campaign);
    await expect(controller.create(dto)).resolves.toEqual(campaign);
  });

  it('should return campaigns based on user role', async () => {
    const campaigns = [{ id: '1' }];
    const mockReq = { user: { id: 'user1', role: 'publisher' } };
    mockCampaignService.findAllForUser.mockResolvedValue(campaigns);
    await expect(controller.findAll(mockReq)).resolves.toEqual(campaigns);
    expect(mockCampaignService.findAllForUser).toHaveBeenCalledWith('user1', 'publisher');
  });

  it('should return one campaign if user has access', async () => {
    const campaign = { id: '1' };
    const mockReq = { user: { id: 'user1', role: 'publisher' } };
    mockCampaignService.findOneForUser.mockResolvedValue(campaign);
    await expect(controller.findOne('1', mockReq)).resolves.toEqual(campaign);
    expect(mockCampaignService.findOneForUser).toHaveBeenCalledWith('1', 'user1', 'publisher');
  });

  it('should throw ForbiddenException if user has no access to campaign', async () => {
    const mockReq = { user: { id: 'user1', role: 'publisher' } };
    mockCampaignService.findOneForUser.mockResolvedValue(null);
    await expect(controller.findOne('1', mockReq)).rejects.toThrow(ForbiddenException);
  });

  it('should update a campaign', async () => {
    const campaign = { id: '1', title: 'B' };
    mockCampaignService.update.mockResolvedValue(campaign);
    await expect(controller.update('1', { title: 'B' })).resolves.toEqual(
      campaign,
    );
  });

  it('should remove a campaign', async () => {
    const campaign = { id: '1' };
    mockCampaignService.remove.mockResolvedValue(campaign);
    await expect(controller.remove('1')).resolves.toEqual(campaign);
  });

  describe('Publisher Assignment', () => {
    it('should assign publishers to campaign', async () => {
      const assignDto = { publisherIds: ['pub1', 'pub2'] };
      const mockReq = { user: { id: 'staff1' } };
      const assignments = [{ id: 'assign1', campaignId: 'camp1', publisherId: 'pub1' }];
      mockCampaignService.assignPublishers.mockResolvedValue(assignments);

      await expect(controller.assignPublishers('camp1', assignDto, mockReq)).resolves.toEqual(assignments);
      expect(mockCampaignService.assignPublishers).toHaveBeenCalledWith('camp1', assignDto, 'staff1');
    });

    it('should remove publishers from campaign', async () => {
      const removeDto = { publisherIds: ['pub1'] };
      const result = { removed: 1 };
      mockCampaignService.removePublishers.mockResolvedValue(result);

      await expect(controller.removePublishers('camp1', removeDto)).resolves.toEqual(result);
      expect(mockCampaignService.removePublishers).toHaveBeenCalledWith('camp1', removeDto);
    });

    it('should get assigned publishers for campaign', async () => {
      const assignments = [{ id: 'assign1', campaignId: 'camp1', publisherId: 'pub1' }];
      mockCampaignService.getAssignedPublishers.mockResolvedValue(assignments);

      await expect(controller.getAssignedPublishers('camp1')).resolves.toEqual(assignments);
      expect(mockCampaignService.getAssignedPublishers).toHaveBeenCalledWith('camp1');
    });
  });
});
