import { IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BroadcastNotificationDto {
  @ApiProperty({
    description: 'Title of the notification',
    example: 'System Maintenance',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Message content of the notification',
    example: 'The system will be unavailable from 1 AM to 2 AM.',
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Segment identifier. If not provided, broadcast to all users.',
    example: 'premium-users',
  })
  @IsString()
  @IsOptional()
  segment?: string; // Optional: segment identifier, if not provided, broadcast to all users
}