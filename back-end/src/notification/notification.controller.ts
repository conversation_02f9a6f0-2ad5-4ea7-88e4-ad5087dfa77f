import { Controller, Get, UseGuards, Req, Patch, Param, Delete, Post, Body } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { NotificationService } from './notification.service';
import { CreateNotificationDto } from './create-notification.dto';
import { BroadcastNotificationDto } from './broadcast-notification.dto';

@ApiTags('Notification')
@ApiBearerAuth('JWT-auth')
@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all notifications for the current user' })
  @ApiResponse({ status: 200, description: 'List of notifications' })
  async getMyNotifications(@Req() req) {
    // Use req.user.id for userId
    return this.notificationService.findAll(req.user.id, 1, 20);
  }

  @Patch(':id/read')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  async markAsRead(@Req() req, @Param('id') id: string) {
    return this.notificationService.markAsRead(req.user.id, id);
  }

  @Patch('read-all')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllAsRead(@Req() req) {
    return this.notificationService.markAllAsRead(req.user.id);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Clear notification' })
  @ApiResponse({ status: 200, description: 'Notification cleared' })
  async clear(@Req() req, @Param('id') id: string) {
    return this.notificationService.clear(req.user.id, id);
  }

  @Delete('clear-all')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Clear all notifications' })
  @ApiResponse({ status: 200, description: 'All notifications cleared' })
  async clearAll(@Req() req) {
    return this.notificationService.clearAll(req.user.id);
  }
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Send a notification to user(s)' })
  @ApiResponse({ status: 201, description: 'Notification sent' })
  async createNotification(@Body() dto: CreateNotificationDto) {
    return this.notificationService.createNotification(dto);
  }

  @Post('broadcast')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Broadcast a notification to all users or a segment' })
  @ApiResponse({ status: 201, description: 'Notification broadcasted' })
  async broadcastNotification(@Body() dto: BroadcastNotificationDto) {
    return this.notificationService.broadcastNotification(dto);
  }
}
