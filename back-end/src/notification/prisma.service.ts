import { Injectable, OnModuleInit, INestApplication } from '@nestjs/common';
import { PrismaClient as PrismaClientType } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClientType implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();
  }

  async enableShutdownHooks(app: INestApplication) {
    // @ts-ignore
    this.$on?.('beforeExit', async () => {
      await app.close();
    });
  }
}