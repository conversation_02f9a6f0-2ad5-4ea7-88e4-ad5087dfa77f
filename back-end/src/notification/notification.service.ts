import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from './prisma.service';

@Injectable()
export class NotificationService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(userId: string, page: number, pageSize: number) {
    const [notifications, total] = await Promise.all([
      this.prisma.notification.findMany({
        where: { userId, clearedAt: null },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      this.prisma.notification.count({ where: { userId, clearedAt: null } }),
    ]);
    return { notifications, total };
  }

  async markAsRead(userId: string, id: string) {
    const notification = await this.prisma.notification.findUnique({ where: { id } });
    if (!notification || notification.userId !== userId || notification.clearedAt) {
      throw new NotFoundException();
    }
    return this.prisma.notification.update({
      where: { id },
      data: { isRead: true, readAt: new Date() },
    });
  }

  async markAllAsRead(userId: string) {
    return this.prisma.notification.updateMany({
      where: { userId, isRead: false, clearedAt: null },
      data: { isRead: true, readAt: new Date() },
    });
  }

  async clear(userId: string, id: string) {
    const notification = await this.prisma.notification.findUnique({ where: { id } });
    if (!notification || notification.userId !== userId || notification.clearedAt) {
      throw new NotFoundException();
    }
    return this.prisma.notification.update({
      where: { id },
      data: { clearedAt: new Date() },
    });
  }

  async clearAll(userId: string) {
    return this.prisma.notification.updateMany({
      where: { userId, clearedAt: null },
      data: { clearedAt: new Date() },
    });
  }
  async createNotification(dto: import('./create-notification.dto').CreateNotificationDto) {
    const { title, message, userIds } = dto;
    if (!userIds || userIds.length === 0) {
      throw new Error('userIds must be provided for direct notification');
    }
    const notifications = await Promise.all(
      userIds.map(userId =>
        this.prisma.notification.create({
          data: {
            userId,
            type: 'custom',
            title,
            message,
            isRead: false,
            readAt: null,
            clearedAt: null,
          },
        }),
      ),
    );
    return { count: notifications.length, notifications };
  }

  async broadcastNotification(dto: import('./broadcast-notification.dto').BroadcastNotificationDto) {
    const { title, message } = dto;
    // Broadcast to all users (no segment support)
    const users = await this.prisma.user.findMany();
    const notifications = await Promise.all(
      users.map(user =>
        this.prisma.notification.create({
          data: {
            userId: user.id,
            type: 'custom',
            title,
            message,
            isRead: false,
            readAt: null,
            clearedAt: null,
          },
        }),
      ),
    );
    return { count: notifications.length, notifications };
  }
}
