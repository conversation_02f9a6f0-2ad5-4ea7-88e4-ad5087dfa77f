import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'Title of the notification',
    example: 'System Update',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Message content of the notification',
    example: 'The system will be down for maintenance at midnight.',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiPropertyOptional({
    description: 'Array of user IDs to send the notification to. If omitted, use broadcast endpoint.',
    type: [String],
    example: ['userId1', 'userId2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  userIds?: string[]; // If omitted, use broadcast endpoint
}