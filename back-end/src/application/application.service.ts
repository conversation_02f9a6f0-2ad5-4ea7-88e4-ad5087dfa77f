import { Injectable } from '@nestjs/common';
import { Application } from '@prisma/client';
import { PrismaService } from '../notification/prisma.service';
import { NotificationService } from '../notification/notification.service';

@Injectable()
export class ApplicationService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
  ) {}

  async create(data: Omit<Application, 'id' | 'createdAt' | 'updatedAt'>): Promise<Application> {
    return this.prisma.application.create({ data });
  }

  async findAll(): Promise<Application[]> {
    return this.prisma.application.findMany();
  }

  async findOne(id: string): Promise<Application | null> {
    return this.prisma.application.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<Omit<Application, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Application> {
    // Get the current application before update
    const prev = await this.prisma.application.findUnique({ where: { id } });
    const updated = await this.prisma.application.update({ where: { id }, data });

    // If status changed to 'approved', notify publisher
    if (
      prev &&
      data.status === 'approved' &&
      prev.status !== 'approved'
    ) {
      await this.notificationService.createNotification({
        title: 'Application Approved',
        message: 'Your application has been approved.',
        userIds: [updated.publisherId],
      });
    }
    return updated;
  }

  async remove(id: string): Promise<Application> {
    return this.prisma.application.delete({ where: { id } });
  }
}