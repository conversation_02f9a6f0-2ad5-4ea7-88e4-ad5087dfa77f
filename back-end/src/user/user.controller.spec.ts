import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { UserService } from './user.service';

describe('UserController', () => {
  let controller: UserController;
  let service: UserService;

  const mockUserService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        { provide: UserService, useValue: mockUserService },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    service = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a user', async () => {
    const dto = { email: '<EMAIL>', passwordHash: 'x', name: 'A', roleId: 'r' };
    const user = { id: '1', ...dto };
    mockUserService.create.mockResolvedValue(user);
    await expect(controller.create(dto)).resolves.toEqual(user);
  });

  it('should return all users', async () => {
    const users = [{ id: '1' }];
    mockUserService.findAll.mockResolvedValue(users);
    await expect(controller.findAll()).resolves.toEqual(users);
  });

  it('should return one user', async () => {
    const user = { id: '1' };
    mockUserService.findOne.mockResolvedValue(user);
    await expect(controller.findOne('1')).resolves.toEqual(user);
  });

  it('should update a user', async () => {
    const user = { id: '1', name: 'B' };
    mockUserService.update.mockResolvedValue(user);
    await expect(controller.update('1', { name: 'B' })).resolves.toEqual(user);
  });

  it('should remove a user', async () => {
    const user = { id: '1' };
    mockUserService.remove.mockResolvedValue(user);
    await expect(controller.remove('1')).resolves.toEqual(user);
  });
});
