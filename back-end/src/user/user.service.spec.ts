import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';

jest.mock('@prisma/client', () => {
  const mUser = {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };
  return {
    PrismaClient: jest.fn(() => ({ user: mUser })),
  };
});

describe('UserService', () => {
  let service: UserService;
  let prisma: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserService],
    }).compile();
    service = module.get<UserService>(UserService);
    prisma = (service as any).prisma.user;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a user', async () => {
    const user = { id: '1', email: '<EMAIL>', passwordHash: 'x', name: 'A', roleId: 'r', createdAt: new Date(), updatedAt: new Date() };
    prisma.create.mockResolvedValue(user);
    await expect(service.create(user)).resolves.toEqual(user);
  });

  it('should find all users', async () => {
    const users = [{ id: '1' }];
    prisma.findMany.mockResolvedValue(users);
    await expect(service.findAll()).resolves.toEqual(users);
  });

  it('should find one user', async () => {
    const user = { id: '1' };
    prisma.findUnique.mockResolvedValue(user);
    await expect(service.findOne('1')).resolves.toEqual(user);
  });

  it('should update a user', async () => {
    const user = { id: '1', name: 'B' };
    prisma.update.mockResolvedValue(user);
    await expect(service.update('1', { name: 'B' })).resolves.toEqual(user);
  });

  it('should remove a user', async () => {
    const user = { id: '1' };
    prisma.delete.mockResolvedValue(user);
    await expect(service.remove('1')).resolves.toEqual(user);
  });
  it('should have getRoleById method', () => {
    expect(typeof service.getRoleById).toBe('function');
  });
});
