import { IsOptional, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateNotificationPreferencesDto {
  @ApiPropertyOptional({
    description: 'Enable or disable email notifications',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  email?: boolean;

  @ApiPropertyOptional({
    description: 'Enable or disable push notifications',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  push?: boolean;
}