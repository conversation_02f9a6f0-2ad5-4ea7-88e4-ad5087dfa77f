import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

type User = {
  id: string;
  email: string;
  passwordHash: string;
  name: string;
  roleId: string;
  createdAt: Date;
  updatedAt: Date;
};

@Injectable()
export class UserService {
  private prisma = new PrismaClient();

  async create(data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    return this.prisma.user.create({ data });
  }

  async findAll(): Promise<User[]> {
    return this.prisma.user.findMany();
  }

  async findOne(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>): Promise<User> {
    return this.prisma.user.update({ where: { id }, data });
  }

  async remove(id: string): Promise<User> {
    return this.prisma.user.delete({ where: { id } });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({ where: { email } });
  }

  async getRoleById(roleId: string): Promise<{ id: string; name: string; description: string | null } | null> {
    return this.prisma.role.findUnique({ 
      where: { id: roleId },
      select: { id: true, name: true, description: true }
    });
  }
  updateRole(id: string, dto: import('./update-user-role.dto').UpdateUserRoleDto) {
    // TODO: Implement logic to update user's role
    // Example: return this.prisma.user.update({ where: { id }, data: { roleId: dto.roleId } });
    return { id, ...dto };
  }

  updateNotificationPreferences(id: string, dto: import('./update-notification-preferences.dto').UpdateNotificationPreferencesDto) {
    // TODO: Implement logic to update user's notification preferences
    // Example: return this.prisma.user.update({ where: { id }, data: { notificationPreferences: dto } });
    return { id, ...dto };
  }
}
