import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserService } from './user.service';
import { AuthGuard } from '@nestjs/passport';
import { Roles } from '../auth/roles.decorator';
import { UpdateUserRoleDto } from './update-user-role.dto';
import { UpdateNotificationPreferencesDto } from './update-notification-preferences.dto';

@UseGuards(AuthGuard('jwt'))
@ApiTags('User')
@ApiBearerAuth()
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: 'Get all users' })
  @ApiResponse({ status: 200, description: 'List of users' })
  findAll() {
    return this.userService.findAll();
  }

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created' })
  create(@Body() data: any) {
    return this.userService.create(data);
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User found' })
  findOne(@Param('id') id: string) {
    return this.userService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update user by ID' })
  @ApiResponse({ status: 200, description: 'User updated' })
  update(@Param('id') id: string, @Body() data: any) {
    return this.userService.update(id, data);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Delete user by ID' })
  @ApiResponse({ status: 200, description: 'User deleted' })
  remove(@Param('id') id: string) {
    return this.userService.remove(id);
  }
  @Patch(':id/role')
  @Roles('admin')
  @ApiOperation({ summary: 'Assign or change a user\'s role' })
  @ApiResponse({ status: 200, description: 'User role updated' })
  updateRole(@Param('id') id: string, @Body() dto: UpdateUserRoleDto) {
    return this.userService.updateRole(id, dto);
  }

  @Patch(':id/notification-preferences')
  @ApiOperation({ summary: 'Update a user\'s notification preferences' })
  @ApiResponse({ status: 200, description: 'Notification preferences updated' })
  updateNotificationPreferences(
    @Param('id') id: string,
    @Body() dto: UpdateNotificationPreferencesDto,
  ) {
    return this.userService.updateNotificationPreferences(id, dto);
  }
}
