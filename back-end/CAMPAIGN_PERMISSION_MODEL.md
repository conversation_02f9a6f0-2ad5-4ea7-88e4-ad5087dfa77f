# Campaign Permission Model Design

## Overview
This document defines the comprehensive permission model for campaign management in the Pay Per Post platform, establishing clear access controls and data visibility rules for different user roles.

## User Roles

### 1. Admin
- **Full System Access**: Complete control over all campaigns, users, and system settings
- **Campaign Management**: Can create, read, update, delete any campaign
- **User Management**: Can manage all users and their roles
- **System Configuration**: Can modify system-wide settings

### 2. Staff (SP Team)
- **Campaign Management**: Can create, read, update, delete any campaign
- **Publisher Assignment**: Can assign specific publishers to campaigns
- **Application Review**: Can approve/reject publisher applications
- **Video Review**: Can review and provide feedback on video submissions
- **Limited User Management**: Can view user information but cannot modify roles

### 3. Advertiser
- **Campaign Viewing**: Can only view campaigns they created (filtered by advertiserId)
- **Campaign Management**: Can update their own campaigns (limited fields)
- **Application Review**: Can view and approve/reject applications for their campaigns
- **Video Review**: Can review video submissions for their campaigns
- **No Creation Rights**: Cannot create new campaigns (only staff/admin can create)

### 4. Publisher
- **Campaign Discovery**: Can only view published campaigns or campaigns specifically assigned to them
- **Application Management**: Can apply to available campaigns
- **Video Submission**: Can submit videos for approved applications
- **Status Tracking**: Can view status of their applications and submissions

## Campaign Visibility Rules

### Campaign Status Types
- `draft`: Campaign is being created/edited
- `published`: Campaign is live and available for applications
- `paused`: Campaign is temporarily inactive
- `completed`: Campaign has ended
- `cancelled`: Campaign was cancelled

### Visibility Matrix

| Role | Draft | Published | Paused | Completed | Cancelled |
|------|-------|-----------|--------|-----------|-----------|
| Admin | All | All | All | All | All |
| Staff | All | All | All | All | All |
| Advertiser | Own only | Own only | Own only | Own only | Own only |
| Publisher | None | Published + Assigned | Assigned only | Assigned only | None |

## Database Schema Updates

### Campaign Table Enhancement
```sql
-- Add assigned publishers support
ALTER TABLE Campaign ADD COLUMN assignedPublisherIds TEXT[] DEFAULT '{}';
```

### New CampaignPublisherAssignment Table (Alternative Approach)
```sql
CREATE TABLE CampaignPublisherAssignment (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaignId UUID NOT NULL REFERENCES Campaign(id) ON DELETE CASCADE,
  publisherId UUID NOT NULL REFERENCES User(id) ON DELETE CASCADE,
  assignedAt TIMESTAMP DEFAULT NOW(),
  assignedBy UUID NOT NULL REFERENCES User(id),
  isActive BOOLEAN DEFAULT TRUE,
  UNIQUE(campaignId, publisherId)
);
```

## API Endpoint Permissions

### Campaign Creation
- **Endpoint**: `POST /campaign`
- **Allowed Roles**: `admin`, `staff`
- **Validation**: Verify user role before allowing creation

### Campaign Listing
- **Endpoint**: `GET /campaign`
- **Role-based Filtering**:
  - `admin/staff`: Return all campaigns
  - `advertiser`: Return only campaigns where `advertiserId = user.id`
  - `publisher`: Return only published campaigns OR campaigns where `user.id IN assignedPublisherIds`

### Campaign Details
- **Endpoint**: `GET /campaign/:id`
- **Access Control**:
  - `admin/staff`: Access any campaign
  - `advertiser`: Access only if `advertiserId = user.id`
  - `publisher`: Access only if campaign is published OR `user.id IN assignedPublisherIds`

### Campaign Updates
- **Endpoint**: `PATCH /campaign/:id`
- **Role-based Access**:
  - `admin`: Can update any field of any campaign
  - `staff`: Can update any field of any campaign
  - `advertiser`: Can update limited fields of own campaigns only
  - `publisher`: No update access

### Publisher Assignment
- **Endpoint**: `POST /campaign/:id/assign-publishers`
- **Allowed Roles**: `admin`, `staff`
- **Payload**: `{ publisherIds: string[] }`

## Implementation Strategy

### Phase 1: Database Schema Update
1. Add `assignedPublisherIds` column to Campaign table
2. Create migration script
3. Update Prisma schema
4. Update DTOs and interfaces

### Phase 2: Backend Permission Logic
1. Update CampaignService with role-based filtering
2. Implement authorization guards
3. Update campaign controller endpoints
4. Add publisher assignment functionality

### Phase 3: API Documentation
1. Update Swagger documentation
2. Add role-based examples
3. Document new endpoints

### Phase 4: Testing
1. Unit tests for permission logic
2. Integration tests for API endpoints
3. Role-based access testing

## Security Considerations

### Data Isolation
- Ensure advertisers cannot access other advertisers' campaigns
- Publishers cannot see draft or private campaigns
- Proper input validation for publisher assignments

### Authorization Checks
- Verify user role on every request
- Validate campaign ownership for advertisers
- Check assignment status for publishers

### Audit Trail
- Log campaign creation and modifications
- Track publisher assignments and removals
- Monitor access patterns for security

## Error Handling

### Common Error Scenarios
- `403 Forbidden`: User lacks permission for requested action
- `404 Not Found`: Campaign doesn't exist or user lacks access
- `400 Bad Request`: Invalid publisher assignment or malformed request

### Error Response Format
```json
{
  "statusCode": 403,
  "message": "Insufficient permissions to access this campaign",
  "error": "Forbidden",
  "details": {
    "requiredRole": ["admin", "staff"],
    "userRole": "publisher"
  }
}
```

## Future Enhancements

### Advanced Permission Features
- Time-based campaign access
- Geographic restrictions
- Publisher category-based assignments
- Granular field-level permissions

### Notification System
- Notify publishers when assigned to campaigns
- Alert advertisers of new applications
- System notifications for status changes

This permission model ensures secure, role-appropriate access to campaign data while maintaining flexibility for future enhancements.
