# Roles Entity Database Schema

## Table: roles

| Field   | Type         | Constraints         | Description         |
|---------|--------------|---------------------|---------------------|
| id      | UUID         | PK, not null, unique| Role ID             |
| name    | VARCHAR(50)  | not null, unique    | Role name (e.g., publisher, advertiser, sp_team) |

## Example: PostgreSQL Migration
```sql
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE
);
```

## Example: Prisma Schema
```prisma
model Role {
  id    String  @id @default(uuid())
  name  String  @unique
  users User[]
}
```

## Notes
- Role name must be unique, e.g.: 'publisher', 'advertiser', 'sp_team'.
- You can extend with additional permissions if needed. 