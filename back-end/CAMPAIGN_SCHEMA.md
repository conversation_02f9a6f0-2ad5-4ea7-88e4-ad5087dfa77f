# Campaigns Entity Database Schema

## Table: campaigns

| Field        | Type           | Constraints                  | Description                        |
|--------------|----------------|------------------------------|------------------------------------|
| id           | UUID           | PK, not null, unique         | Campaign ID                        |
| title        | VARCHAR(255)   | not null                     | Campaign title                     |
| description  | TEXT           | not null                     | Campaign description               |
| advertiser_id| UUID           | FK → users.id, not null      | Advertiser (user) who owns campaign|
| start_date   | DATE           | not null                     | Campaign start date                |
| end_date     | DATE           | not null                     | Campaign end date                  |
| status       | VARCHAR(50)    | not null                     | Status (draft, active, closed, etc.)|
| created_at   | TIMESTAMP      | not null, default now()      | Creation timestamp                 |
| updated_at   | TIMESTAMP      | not null, default now()      | Last update timestamp              |

## Example: PostgreSQL Migration
```sql
CREATE TABLE campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  advertiser_id UUID NOT NULL REFERENCES users(id),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  status VARCHAR(50) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## Example: Prisma Schema
```prisma
model Campaign {
  id           String   @id @default(uuid())
  title        String
  description  String
  advertiser   User     @relation(fields: [advertiserId], references: [id])
  advertiserId String
  startDate    DateTime
  endDate      DateTime
  status       String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  applications Application[]
}
```
