# DETAILED MVP Plan: Pay Per Post - Endorsement Dashboard

## 1. MVP Goals

- Allow Publishers to apply for campaigns, upload videos, and receive feedback.
- Allow SP Team and Advertiser to approve/reject applications and videos.
- Track the status of campaigns and videos.

---

## 2. Core Features - Detailed Breakdown

### 2.1. Authentication & Role Management

*   **Backend (NestJS):**
    *   Design and implement database schema for `Users` (including roles: Publisher, SP Team, Advertiser) and `Roles`.
    *   Develop API endpoint for user login (`POST /auth/login`) with username/password authentication.
    *   Implement JWT token generation upon successful login and validation middleware for protected routes.
    *   Implement Role-Based Access Control (RBAC) using NestJS guards and decorators to restrict API access based on user roles.
    *   Create basic API endpoints for user profile retrieval (`GET /users/me`) and potentially password change.
*   **Frontend (ReactJS/Next.js):**
    *   Design and implement a responsive login user interface.
    *   Integrate with the backend login API, handling successful authentication and error states.
    *   Implement client-side storage and management of JWT tokens (e.g., using HttpOnly cookies or local storage).
    *   Set up protected routes in Next.js to ensure only authenticated users can access dashboard areas.
    *   Dynamically render navigation and dashboard components based on the authenticated user's role.

### 2.2. Campaign Management

*   **Backend (NestJS):**
    *   Design and implement database schema for `Campaigns` (e.g., `id`, `name`, `description`, `status`, `advertiserId`).
    *   Implement API endpoints for SP Team and Advertisers to create, update, and delete campaigns (`POST /campaigns`, `PUT /campaigns/:id`, `DELETE /campaigns/:id`).
    *   Implement API endpoint for Publishers to view a list of active campaigns (`GET /campaigns/publisher`).
    *   Implement API endpoint for Publishers to view detailed information of a specific campaign (`GET /campaigns/:id`).
    *   Implement API endpoint for Publishers to apply to a campaign (`POST /campaigns/:id/apply`), creating an `Application` record with initial status (e.g., 'Pending SP Review').
*   **Frontend (ReactJS/Next.js):**
    *   **Publisher Dashboard:**
        *   Develop a "Campaign List" page displaying available campaigns with key details.
        *   Implement a "Campaign Detail" page showing full campaign information and an "Apply" button.
        *   Integrate with backend APIs to fetch campaign data and submit applications.
        *   Display the current application status for campaigns the publisher has applied to.

### 2.3. Application Approval Workflow

*   **Backend (NestJS):**
    *   Update `Application` schema to include status (e.g., 'Pending SP Review', 'Pending Advertiser Review', 'Approved', 'Rejected').
    *   Implement API endpoint for SP Team to review and update the status of applications (`PUT /applications/:id/sp-review`), allowing approval or rejection.
    *   Implement API endpoint for Advertisers to review and update the status of applications (`PUT /applications/:id/advertiser-review`), allowing final approval or rejection.
    *   Ensure strict RBAC for these endpoints, allowing only authorized SP Team or Advertiser users to perform actions.
*   **Frontend (ReactJS/Next.js):**
    *   **SP Team Dashboard:**
        *   Develop a dedicated section to view all pending applications for SP Team review.
        *   Implement UI elements (buttons, dropdowns) for SP Team to approve or reject applications.
    *   **Advertiser Dashboard:**
        *   Develop a dedicated section to view applications that have passed SP Team review and are pending Advertiser approval.
        *   Implement UI elements for Advertisers to approve or reject applications.
    *   Display clear status indicators for each application in both SP Team and Advertiser dashboards.

### 2.4. Video Management

*   **Backend (NestJS):**
    *   Design and implement database schema for `VideoSubmissions` (e.g., `id`, `campaignId`, `publisherId`, `videoUrl`, `status`, `feedback`).
    *   Integrate with a chosen video storage service (AWS S3 or Cloudinary) for secure video uploads.
    *   Implement API endpoint for Publishers to upload video files directly or submit a video URL (`POST /videos/upload`).
    *   Implement API endpoint for SP Team to review and update video status (`PUT /videos/:id/sp-review`), including providing feedback for rejection.
    *   Implement API endpoint for Advertisers to review and update video status (`PUT /videos/:id/advertiser-review`), including final approval or rejection.
    *   Implement API endpoint for Publishers to submit the final public link of the approved video (`PUT /videos/:id/submit-link`).
*   **Frontend (ReactJS/Next.js):**
    *   **Publisher Dashboard:**
        *   Develop a "Video Submission" interface allowing Publishers to upload video files or paste video links.
        *   Display the current status of submitted videos (e.g., 'Pending SP Review', 'Rejected by SP', 'Pending Advertiser Review', 'Approved', 'Link Submitted').
        *   Provide a mechanism for Publishers to re-upload or resubmit videos if rejected.
        *   Implement a form for Publishers to submit the final public link of the approved video.
    *   **SP Team/Advertiser Dashboards:**
        *   Develop a section to view submitted videos, including a video player or link to view the video.
        *   Implement UI for SP Team and Advertisers to approve/reject videos, with a text area for feedback on rejection.

### 2.5. Status Tracking & Notification

*   **Backend (NestJS):**
    *   Implement robust status tracking within the `Application` and `VideoSubmission` database models.
    *   Integrate with an email notification service (SendGrid or Mailgun).
    *   Develop a notification service module to send automated emails to relevant users upon status changes (e.g., Publisher: "Your application has been approved!", SP Team: "New video submitted for review").
    *   Define email templates for different notification types.
*   **Frontend (ReactJS/Next.js):**
    *   Ensure all dashboards (Publisher, SP Team, Advertiser) clearly display the current status of applications and videos.
    *   Implement basic in-app notifications or alerts for critical status changes (optional for MVP, but enhances UX).

### 1.x. Local Development Environment with Docker

*   Use Docker Compose to run PostgreSQL and any other required services for backend development.
*   Developers should use Docker for consistent local environments and easy onboarding.
*   Update documentation to include Docker-based setup and usage instructions.

---

## 3. Technology Stack Suggestion

| Component | Suggested Technology |
|---|---|
| Frontend | ReactJS (Next.js) |
| Backend | NestJS + OpenAPI (API First Approach) |
| Database | PostgreSQL/MySQL |
| Auth | JWT or Firebase Auth |
| Video Storage | AWS S3 or Cloudinary |
| Notification | Email (SendGrid, Mailgun) |
| Deployment | Vercel/Heroku/AWS |

---

## 4. Workflow Diagram (Mermaid)

```mermaid
flowchart TD
    Start([Start])
    ViewCampaign[Publisher: View Campaigns]
    ViewDetail[Publisher: View Campaign Detail]
    Apply[Publisher: Apply Campaign]
    SPApprove[SP Team: Approve Join?]
    AdvApprove[Advertiser: Approve Join?]
    UploadVideo[Publisher: Upload Video]
    SPVideoApprove[SP Team: Approve Video?]
    AdvVideoApprove[Advertiser: Approve Video?]
    UploadSite[Publisher: Upload to Site & Share Link]
    Confirm[Advertiser: Confirm & Monitor]
    Reject[Rejected/End]

    Start --> ViewCampaign
    ViewCampaign --> ViewDetail
    ViewDetail --> Apply
    Apply --> SPApprove
    SPApprove -- No --> Reject
    SPApprove -- Yes --> AdvApprove
    AdvApprove -- No --> Reject
    AdvApprove -- Yes --> UploadVideo
    UploadVideo --> SPVideoApprove
    SPVideoApprove -- No --> UploadVideo
    SPVideoApprove -- Yes --> AdvVideoApprove
    AdvVideoApprove -- No --> Reject
    AdvVideoApprove -- Yes --> UploadSite
    UploadSite --> Confirm
    Confirm --> Reject
```

---

## 5. Development Roadmap - Detailed Breakdown

### Week 1-2: Foundation & Authentication

*   **System Architecture & Database Design:**
    *   Finalize overall system architecture, including service boundaries and data flow.
    *   Create detailed database schemas for `Users`, `Roles`, `Campaigns`, `Applications`, and `VideoSubmissions`.
    *   Set up initial project repositories for Next.js (frontend) and NestJS (backend).
    *   Configure database connection and ORM (TypeORM/Prisma) for NestJS.
*   **Authentication & Role Management Implementation:**
    *   Implement `User` and `Role` entities/models in NestJS.
    *   Develop authentication module with login endpoint and JWT strategy.
    *   Implement RBAC guards and decorators for API route protection.
    *   Create initial seed data for different user roles (Publisher, SP Team, Advertiser) for testing.
    *   Develop the login page UI in Next.js.
    *   Integrate frontend login with backend API, handling JWT token storage.
    *   Implement client-side routing protection based on user roles.
    *   Develop basic dashboard layouts for each user role.

### Week 3: Campaign Management & Initial Dashboards

*   **Campaign Management Module Development:**
    *   **Backend:** Implement `Campaign` entity/model and CRUD API endpoints for SP Team/Advertiser.
    *   **Backend:** Implement API endpoints for Publishers to list and view campaign details.
    *   **Backend:** Implement API endpoint for Publishers to apply to campaigns, creating `Application` records.
    *   **Frontend (Publisher):** Build "Campaign List" and "Campaign Detail" pages.
    *   **Frontend (Publisher):** Implement functionality to apply to a campaign.
*   **Dashboard Refinement:**
    *   **Frontend:** Refine Publisher Dashboard to display applied campaigns and their current status.
    *   **Frontend:** Create initial SP Team Dashboard to list pending applications.
    *   **Frontend:** Create initial Advertiser Dashboard to list pending applications.

### Week 4: Approval Workflows & Video Upload

*   **Application Approval Workflow Implementation:**
    *   **Backend:** Implement API endpoints for SP Team to approve/reject applications.
    *   **Backend:** Implement API endpoints for Advertisers to approve/reject applications.
    *   **Frontend (SP Team/Advertiser):** Develop UI for reviewing and updating application statuses.
*   **Video Upload & Review Features Implementation:**
    *   **Backend:** Integrate NestJS with AWS S3 or Cloudinary for video storage.
    *   **Backend:** Implement API endpoint for Publishers to upload videos or submit video URLs.
    *   **Backend:** Implement API endpoints for SP Team to approve/reject videos (with feedback).
    *   **Backend:** Implement API endpoints for Advertisers to approve/reject videos.
    *   **Frontend (Publisher):** Develop video upload/submission UI.
    *   **Frontend (SP Team/Advertiser):** Develop UI for viewing submitted videos and performing approval/rejection actions.

### Week 5: Video Publishing & Notifications

*   **Video Publishing & Link Submission Implementation:**
    *   **Backend:** Implement API endpoint for Publishers to submit the final public video link.
    *   **Frontend (Publisher):** Develop UI for submitting the public video link after video approval.
*   **Status Notification (Email) Implementation:**
    *   **Backend:** Integrate NestJS with SendGrid or Mailgun.
    *   **Backend:** Develop a notification service to send automated emails on application and video status changes.
    *   **Backend:** Define and implement email templates for various notifications.

### Week 6: Testing, Polish & Deployment

*   **Comprehensive Testing:**
    *   Conduct unit tests for all backend services and frontend components.
    *   Perform integration tests for API endpoints and data flows.
    *   Execute end-to-end tests covering core user workflows (login, apply, approve, upload, approve video, submit link).
    *   Identify and resolve all reported bugs.
*   **Basic UI/UX Polish:**
    *   Apply consistent styling across the application using a chosen UI framework or custom CSS.
    *   Ensure basic responsiveness for key dashboard views on different screen sizes.
    *   Improve user feedback mechanisms (e.g., loading states, success/error messages).
*   **MVP Deployment:**
    *   Prepare deployment configurations for the chosen platforms (Vercel for frontend, Heroku/AWS for backend).
    *   Deploy the frontend and backend applications to production environments.
    *   Perform final smoke tests on the live MVP to ensure all functionalities are working as expected.

---

## 6. MVP Priorities

- Focus only on the main workflow, keep UI simple.
- No need for payment integration, advanced reporting, or analytics in MVP.

---

## 7. Future Enhancements

- Payment Integration for Publishers.
- Advanced Reporting and Analytics Dashboard.
- In-app Chat/Messaging System.
- Multi-language Support.