name: CI - Frontend & Backend

on:
  pull_request:
    branches: [develop, main, master]

jobs:
  backend:
    name: Backend Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: back-end
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm install
      - run: npm run build --if-present
      - run: npm test

  frontend:
    name: Frontend Test
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: front-end
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm install
      - run: npm run build --if-present
      - run: npm test 