'use client';
import React, { useState } from 'react';
import { ReactSortable } from 'react-sortablejs';

const items1 = [
    {
        id: 1,
        text: 'Need to be approved',
        name: '<PERSON>',
    },
    {
        id: 2,
        text: 'Meeting with client',
        name: '<PERSON>',
    },
    {
        id: 3,
        text: 'Project Detail',
        name: '<PERSON>',
    },
    {
        id: 4,
        text: 'Edited Post Approval',
        name: '<PERSON>',
    },
    {
        id: 5,
        text: 'Project Lead Pickup',
        name: '<PERSON>',
    },
];
const items2 = [
    {
        id: 6,
        text: 'Need to be approved',
        name: '<PERSON>',
    },
    {
        id: 7,
        text: 'Meeting with client',
        name: '<PERSON>',
    },
    {
        id: 8,
        text: 'Project Detail',
        name: '<PERSON>',
    },
    {
        id: 9,
        text: 'Edited Post Approval',
        name: '<PERSON>',
    },
    {
        id: 10,
        text: 'Project Lead Pickup',
        name: '<PERSON>',
    },
];
const ComponentsDragndropSortable = () => {
    const [sortable1, setSortable1] = useState(items1);
    const [sortable2, setSortable2] = useState(items2);
    return (
        <div className="panel">
            <div className="mb-5 text-lg font-semibold">Sortable</div>
            <div className="grid grid-cols-1 gap-x-12 sm:grid-cols-2">
                <div>
                    <ul id="example1">
                        <ReactSortable list={sortable1} setList={setSortable1} animation={200} delay={2} ghostClass="gu-transit" group="shared">
                            {sortable1.map((item) => {
                                return (
                                    <li key={item.id} className="mb-2.5 cursor-grab">
                                        <div className="items-md-center flex flex-col rounded-md border border-white-light bg-white px-6 py-3.5 text-center dark:border-dark dark:bg-[#1b2e4b] md:flex-row ltr:md:text-left rtl:md:text-right">
                                            <div className="ltr:sm:mr-4 rtl:sm:ml-4">
                                                <img alt="avatar" src={`/assets/images/profile-${item.id}.jpeg`} className="mx-auto h-11 w-11 rounded-full" />
                                            </div>
                                            <div className="flex flex-1 flex-col items-center justify-between md:flex-row">
                                                <div className="my-3 font-semibold md:my-0">
                                                    <div className="text-base text-dark dark:text-[#bfc9d4]">{item.text}</div>
                                                    <div className="text-xs text-white-dark">{item.name}</div>
                                                </div>
                                                <div>
                                                    <button type="button" className="btn btn-secondary btn-sm px-5 py-2">
                                                        View
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                );
                            })}
                        </ReactSortable>
                    </ul>
                </div>
                <div>
                    <ul id="example2">
                        <ReactSortable list={sortable2} setList={setSortable2} animation={200} delay={2} ghostClass="gu-transit" group="shared">
                            {sortable2.map((item) => {
                                return (
                                    <li key={item.id} className="mb-2.5 cursor-grab">
                                        <div className="items-md-center flex flex-col rounded-md border border-white-light bg-white px-6 py-3.5 text-center dark:border-dark dark:bg-[#1b2e4b] md:flex-row ltr:md:text-left rtl:md:text-right">
                                            <div className="ltr:sm:mr-4 rtl:sm:ml-4">
                                                <img alt="avatar" src={`/assets/images/profile-${item.id}.jpeg`} className="mx-auto h-11 w-11 rounded-full" />
                                            </div>
                                            <div className="flex flex-1 flex-col items-center justify-between md:flex-row">
                                                <div className="my-3 font-semibold md:my-0">
                                                    <div className="text-base text-dark dark:text-[#bfc9d4]">{item.text}</div>
                                                    <div className="text-xs text-white-dark">{item.name}</div>
                                                </div>
                                                <div>
                                                    <button type="button" className="btn btn-secondary btn-sm px-5 py-2">
                                                        View
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                );
                            })}
                        </ReactSortable>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default ComponentsDragndropSortable;
