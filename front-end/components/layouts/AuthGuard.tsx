"use client";
import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { selectAuth, loadAuthFromStorage } from "@/store/authSlice";

const AuthGuard = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { token, isLoading } = useSelector(selectAuth);

  useEffect(() => {
    dispatch(loadAuthFromStorage());
  }, [dispatch]);

  useEffect(() => {
    if (!isLoading && !token) {
      router.replace("/login");
    }
  }, [token, isLoading, router]);

  if (!token) {
    return null; 
  }
  return <>{children}</>;
};

export default AuthGuard;
