{"name": "vristo-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.10.6", "@fullcalendar/core": "^6.1.4", "@fullcalendar/daygrid": "^6.1.4", "@fullcalendar/interaction": "^6.1.1", "@fullcalendar/react": "^6.1.4", "@fullcalendar/timegrid": "^6.1.1", "@headlessui/react": "^1.7.8", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@reduxjs/toolkit": "^1.9.1", "@tippyjs/react": "^4.2.6", "@types/node": "18.11.18", "@types/react": "18.0.27", "@types/react-dom": "18.0.10", "@x1mrdonut1x/nouislider-react": "^3.4.3", "apexcharts": "^3.37.1", "easymde": "^2.18.0", "eslint": "8.32.0", "eslint-config-next": "13.1.2", "formik": "^2.2.9", "highlight.js": "^11.7.0", "i18next": "^22.4.10", "mantine-datatable": "^1.7.35", "next": "14.0.3", "ni18n": "^1.0.5", "react": "18.2.0", "react-animate-height": "^3.1.0", "react-apexcharts": "^1.4.0", "react-click-away-listener": "^2.2.2", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.4.1", "react-dom": "18.2.0", "react-flatpickr": "^3.10.13", "react-i18next": "^12.1.5", "react-images-uploading": "^3.1.7", "react-perfect-scrollbar": "^1.5.8", "react-popper": "^2.3.0", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-select": "^5.7.0", "react-simplemde-editor": "^5.2.0", "react-sortablejs": "^6.1.4", "react-text-mask": "^5.5.0", "sortablejs": "^1.15.0", "sweetalert2": "^11.7.1", "sweetalert2-react-content": "^5.0.7", "swiper": "^8.4.7", "typescript": "^5.3.3", "universal-cookie": "^6.1.1", "yet-another-react-lightbox": "^3.15.6", "yup": "^0.32.11"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.8", "@types/lodash": "^4.14.191", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-flatpickr": "^3.8.8", "@types/react-redux": "^7.1.32", "@types/react-text-mask": "^5.4.11", "@types/sortablejs": "^1.15.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "prettier": "^2.8.0", "prettier-plugin-tailwindcss": "^0.2.0", "tailwindcss": "^3.4.1"}}