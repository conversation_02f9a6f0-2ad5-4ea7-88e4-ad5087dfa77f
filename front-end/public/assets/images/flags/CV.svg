<?xml version="1.0" encoding="UTF-8"?>
<svg width="21px" height="15px" viewBox="0 0 21 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: sketchtool 46 (44423) - http://www.bohemiancoding.com/sketch -->
    <title>CV</title>
    <desc>Created with sketchtool.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F0F0F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#0C49AE" offset="0%"></stop>
            <stop stop-color="#063B91" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#CD232E" offset="0%"></stop>
            <stop stop-color="#CD232E" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="CV">
            <rect id="FlagBackground" fill="url(#linearGradient-1)" x="0" y="0" width="21" height="15"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="0" width="21" height="8"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-2)" x="0" y="11" width="21" height="4"></rect>
            <rect id="Rectangle-2" fill="url(#linearGradient-1)" x="0" y="8" width="21" height="3"></rect>
            <rect id="Rectangle-2-Copy-4" fill="url(#linearGradient-3)" x="0" y="9" width="21" height="1"></rect>
            <path d="M8,13 C7.72385763,13 7.5,12.7761424 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 C8.28367896,12 8.56025161,11.9529391 8.82192552,11.8618903 C9.08273128,11.7711436 9.36772087,11.9090037 9.45846757,12.1698095 C9.54921428,12.4306153 9.41135418,12.7156048 9.15054841,12.8063515 C8.78356688,12.9340418 8.39593675,13 8,13 Z M10.6511931,11.7850663 C10.906745,11.4887971 11.1113949,11.1516075 11.255598,10.7871258 C11.3571885,10.5303497 11.2313858,10.2398361 10.9746097,10.1382455 C10.7178336,10.036655 10.42732,10.1624577 10.3257294,10.4192338 C10.2228617,10.6792381 10.076689,10.9200782 9.89396942,11.1319106 C9.71360547,11.3410122 9.73690189,11.6567364 9.94600344,11.8371003 C10.155105,12.0174643 10.4708291,11.9941678 10.6511931,11.7850663 Z M11.4681136,9.02598701 C11.4151393,8.63525776 11.2969147,8.25940768 11.1195772,7.91169522 C10.9941162,7.66569899 10.6929906,7.56798589 10.4469943,7.69344691 C10.2009981,7.81890792 10.103285,8.12003351 10.228746,8.36602974 C10.3551834,8.6139404 10.4393986,8.88167049 10.4771796,9.16033603 C10.514279,9.43397492 10.7661822,9.62572801 11.0398211,9.58862856 C11.31346,9.5515291 11.5052131,9.2996259 11.4681136,9.02598701 Z M9.85663172,6.53255799 C9.52534086,6.32490498 9.16108865,6.1738376 8.77747146,6.08677105 C8.50817789,6.02565162 8.24032524,6.19441011 8.17920581,6.46370368 C8.11808639,6.73299725 8.28684488,7.00084991 8.55613845,7.06196933 C8.82950869,7.12401401 9.0891154,7.23168146 9.32553666,7.37987023 C9.55951548,7.52652808 9.86808247,7.45574046 10.0147403,7.22176164 C10.1613982,6.98778282 10.0906105,6.67921584 9.85663172,6.53255799 Z M7.0575528,6.12848171 C6.67912609,6.23407333 6.32257836,6.40271264 6.0015931,6.62628761 C5.77499938,6.7841163 5.71925431,7.09575224 5.87708301,7.32234596 C6.0349117,7.54893969 6.34654764,7.60468475 6.57314136,7.44685606 C6.80235338,7.28720365 7.05662191,7.16694018 7.32631439,7.09168844 C7.59229658,7.01747198 7.74775343,6.74168648 7.67353696,6.47570428 C7.5993205,6.20972209 7.323535,6.05426525 7.0575528,6.12848171 Z M4.84980403,7.9732204 C4.6794218,8.32421179 4.56864103,8.70226234 4.52331764,9.09421416 C4.49159725,9.36852863 4.68825862,9.6166189 4.96257309,9.64833928 C5.23688756,9.68005966 5.48497782,9.4833983 5.51669821,9.20908383 C5.54903512,8.9294377 5.62794951,8.6601344 5.74941219,8.40991841 C5.87000302,8.16149848 5.76637705,7.86235615 5.51795712,7.74176532 C5.26953719,7.6211745 4.97039486,7.72480046 4.84980403,7.9732204 Z M4.79030361,10.8975596 C4.94684558,11.256651 5.16282722,11.5866698 5.42825784,11.874078 C5.61560955,12.0769424 5.93194217,12.0895179 6.13480653,11.9021661 C6.33767089,11.7148144 6.35024638,11.3984818 6.16289467,11.1956174 C5.97304982,10.9900535 5.81870545,10.7542161 5.70698559,10.4979421 C5.59663426,10.2448073 5.30197059,10.1290585 5.04883585,10.2394098 C4.79570111,10.3497611 4.67995228,10.6444248 4.79030361,10.8975596 Z M6.93580227,12.8351354 C6.67271015,12.7512477 6.52743627,12.469965 6.61132398,12.2068729 C6.69521168,11.9437808 6.97649441,11.7985069 7.23958653,11.8823946 C7.46870828,11.9554507 7.70854545,11.9951777 7.954027,11.9995874 C8.23012484,12.0045471 8.44992573,12.2323893 8.44496604,12.5084871 C8.44000635,12.7845849 8.21216421,13.0043858 7.93606638,12.9994261 C7.59324509,12.9932679 7.25713989,12.9375949 6.93580227,12.8351354 Z" id="Oval-73" fill="#F7D035" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>