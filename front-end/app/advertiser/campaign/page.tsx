"use client";
import React, { useState } from "react";

const CreateCampaignPage = () => {
  const [form, setForm] = useState({
    title: "",
    description: "",
    startDate: "",
    endDate: "",
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);
    try {
      const res = await fetch("/api/campaigns", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form),
      });
      if (!res.ok) throw new Error("Failed to create campaign");
      setSuccess(true);
      setForm({ title: "", description: "", startDate: "", endDate: "" });
    } catch (err) {
      if (err instanceof Error) setError(err.message);
      else setError("Unknown error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto mt-10">
      <div className="panel p-8 shadow-lg rounded-lg bg-white dark:bg-black">
        <h1 className="text-2xl font-bold mb-6 text-primary">Create Campaign</h1>
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label htmlFor="title" className="block font-semibold mb-1">Title</label>
            <input
              id="title"
              name="title"
              value={form.title}
              onChange={handleChange}
              placeholder="Campaign Title"
              className="form-input w-full"
              required
            />
          </div>
          <div>
            <label htmlFor="description" className="block font-semibold mb-1">Description</label>
            <textarea
              id="description"
              name="description"
              value={form.description}
              onChange={handleChange}
              placeholder="Describe your campaign..."
              className="form-input w-full min-h-[100px]"
              required
            />
          </div>
          <div className="flex gap-4">
            <div className="flex-1">
              <label htmlFor="startDate" className="block font-semibold mb-1">Start Date</label>
              <input
                id="startDate"
                name="startDate"
                type="date"
                value={form.startDate}
                onChange={handleChange}
                className="form-input w-full"
                required
              />
            </div>
            <div className="flex-1">
              <label htmlFor="endDate" className="block font-semibold mb-1">End Date</label>
              <input
                id="endDate"
                name="endDate"
                type="date"
                value={form.endDate}
                onChange={handleChange}
                className="form-input w-full"
                required
              />
            </div>
          </div>
          <button
            type="submit"
            className="btn btn-gradient w-full uppercase font-semibold mt-4"
            disabled={loading}
          >
            {loading ? "Creating..." : "Create Campaign"}
          </button>
          {success && <div className="text-green-600 font-semibold text-center">Campaign created successfully!</div>}
          {error && <div className="text-red-600 font-semibold text-center">{error}</div>}
        </form>
      </div>
    </div>
  );
};

export default CreateCampaignPage;
