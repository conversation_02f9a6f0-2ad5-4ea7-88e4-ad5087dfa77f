"use client";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { selectAuth } from "@/store/authSlice";
import { useRouter } from "next/navigation";

const AdvertiserDashboard = () => {
  const { user } = useSelector(selectAuth);
  const router = useRouter();

  // Notification state
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: "Welcome to your dashboard!",
      type: "info",
      read: false,
      timestamp: new Date().toLocaleTimeString(),
    },
  ]);
  const [notificationPrefs, setNotificationPrefs] = useState({ email: true, push: false });
  const [toast, setToast] = useState<{ message: string; type: string } | null>(null);

  // Simulate status for each stat
  const statStatuses = ["active", "success", "pending", "error"];

  // Notification handlers
  const handleMarkRead = (id: number) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)));
  };
  const handleClearAll = () => setNotifications([]);
  const handlePreferencesChange = (prefs: { email: boolean; push: boolean }) => {
    setNotificationPrefs(prefs);
    setToast({ message: "Preferences updated", type: "success" });
  };

  // Simulate new notification (real-time)
  useEffect(() => {
    const timer = setTimeout(() => {
      setNotifications((prev) => [
        ...prev,
        {
          id: prev.length + 1,
          message: "You have a new campaign update.",
          type: "info",
          read: false,
          timestamp: new Date().toLocaleTimeString(),
        },
      ]);
    }, 15000);
    return () => clearTimeout(timer);
  }, []);

  // Stat data
  const stats = [
    {
      label: "Active Campaigns",
      value: 8,
      icon: <span className="text-purple-700 text-2xl">📢</span>,
      status: statStatuses[0],
    },
    {
      label: "Total Reach",
      value: "120K",
      icon: <span className="text-green-700 text-2xl">🌐</span>,
      status: statStatuses[1],
    },
    {
      label: "Pending Applications",
      value: 15,
      icon: <span className="text-yellow-700 text-2xl">👥</span>,
      status: statStatuses[2],
    },
    {
      label: "Content to Review",
      value: 4,
      icon: <span className="text-blue-700 text-2xl">📝</span>,
      status: statStatuses[3],
    },
  ];

  // Chart placeholder
  const chart = (
    <div className="h-48 flex items-center justify-center text-gray-400 text-4xl">📈 Chart goes here</div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-2 text-gray-900">Welcome to Your Brand Dashboard</h2>
          <p className="text-gray-600">
            Create impactful influencer marketing campaigns, review creator applications, and track content performance. Amplify your brand reach with authentic content.
          </p>
        </div>
        {/* Stat Widgets */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat, idx) => (
            <div key={stat.label} className="flex flex-col gap-2 panel p-6 items-center">
              <div className="mb-2">{stat.icon}</div>
              <div className="text-lg font-semibold">{stat.value}</div>
              <div className="text-gray-500">{stat.label}</div>
              <div className="text-xs text-gray-400 mt-1">{stat.status}</div>
            </div>
          ))}
        </div>
        {/* Chart Widget */}
        <div className="panel mb-8 p-6">
          <div className="font-semibold mb-2">Campaign Reach Over Time</div>
          {chart}
        </div>
        {/* Action Cards */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {/* Only staff can see Create Campaign */}
          {user?.role === 'staff' && (
            <div className="panel p-6 flex flex-col">
              <div className="flex items-center gap-2 font-semibold mb-2">
                <span className="text-purple-700 text-2xl">📢</span> Create New Campaign
              </div>
              <div className="text-gray-600 mb-4">Launch a new influencer marketing campaign</div>
              <button
                className="w-full py-2 px-4 bg-purple-700 text-white rounded hover:bg-purple-800 font-medium transition"
                onClick={() => router.push("/advertiser/campaigns/new")}
              >
                New Campaign
              </button>
            </div>
          )}
          <div className="panel p-6 flex flex-col">
            <div className="flex items-center gap-2 font-semibold mb-2">
              <span className="text-purple-700 text-2xl">⚙️</span> Campaign Management
            </div>
            <div className="text-gray-600 mb-4">Monitor and manage your active campaigns</div>
            <button
              className="w-full py-2 px-4 border border-purple-700 text-purple-700 rounded hover:bg-purple-50 font-medium transition"
              onClick={() => router.push("/advertiser/campaigns")}
            >
              Manage Campaigns
            </button>
          </div>
          <div className="panel p-6 flex flex-col">
            <div className="flex items-center gap-2 font-semibold mb-2">
              <span className="text-purple-700 text-2xl">👥</span> Creator Applications
            </div>
            <div className="text-gray-600 mb-4">Review and approve content creator applications</div>
            <button
              className="w-full py-2 px-4 border border-purple-700 text-purple-700 rounded hover:bg-purple-50 font-medium transition"
              onClick={() => router.push("/advertiser/applications")}
            >
              Review Applications
            </button>
          </div>
          <div className="panel p-6 flex flex-col">
            <div className="flex items-center gap-2 font-semibold mb-2">
              <span className="text-purple-700 text-2xl">📝</span> Content Review
            </div>
            <div className="text-gray-600 mb-4">Review and approve submitted content</div>
            <button
              className="w-full py-2 px-4 border border-purple-700 text-purple-700 rounded hover:bg-purple-50 font-medium transition"
              onClick={() => router.push("/advertiser/content-review")}
            >
              Review Content
            </button>
          </div>
        </div>
      </main>
      {/* Notification Center */}
      {showNotifications && (
        <div className="fixed top-20 right-8 z-50 bg-white shadow-lg rounded-lg p-4 w-80">
          <div className="flex items-center justify-between mb-2">
            <span className="font-semibold">Notifications</span>
            <button className="text-xs text-gray-500" onClick={handleClearAll}>Clear All</button>
          </div>
          <div className="mb-2">
            {notifications.length === 0 && <div className="text-gray-400 text-sm">No notifications</div>}
            {notifications.map((n) => (
              <div key={n.id} className={`p-2 rounded mb-1 ${n.read ? "bg-gray-100" : "bg-purple-50"}`}>
                <div className="flex items-center justify-between">
                  <span>{n.message}</span>
                  {!n.read && (
                    <button className="text-xs text-purple-700" onClick={() => handleMarkRead(n.id)}>Mark as read</button>
                  )}
                </div>
                <div className="text-xs text-gray-400">{n.timestamp}</div>
              </div>
            ))}
          </div>
          <div className="mt-2">
            <label className="block text-xs font-semibold mb-1">Preferences</label>
            <div className="flex gap-2 items-center mb-1">
              <input type="checkbox" checked={notificationPrefs.email} onChange={e => handlePreferencesChange({ ...notificationPrefs, email: e.target.checked })} />
              <span className="text-xs">Email</span>
              <input type="checkbox" checked={notificationPrefs.push} onChange={e => handlePreferencesChange({ ...notificationPrefs, push: e.target.checked })} />
              <span className="text-xs">Push</span>
            </div>
          </div>
        </div>
      )}
      {/* Toast */}
      {toast && (
        <div className={`fixed bottom-8 right-8 z-50 px-4 py-2 rounded shadow-lg text-white ${toast.type === "success" ? "bg-green-600" : "bg-red-600"}`}>
          {toast.message}
          <button className="ml-2 text-xs underline" onClick={() => setToast(null)}>Close</button>
        </div>
      )}
    </div>
  );
};

export default AdvertiserDashboard;
