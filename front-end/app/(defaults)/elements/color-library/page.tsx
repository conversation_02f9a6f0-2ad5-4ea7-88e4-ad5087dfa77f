import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
    title: 'Color Library',
};

const ColorLibrary = () => {
    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="#" className="text-primary hover:underline">
                        Elements
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Color Library</span>
                </li>
            </ul>
            <div className="space-y-8 pt-5">
                {/* main */}
                <div>
                    <div className="mb-5 w-fit">
                        <h5 className="rounded bg-success/20 px-3 py-1 text-base font-semibold text-success">Main Colors</h5>
                    </div>
                    <div className="mb-5">
                        <div className="mb-7 grid grid-cols-2 gap-4 font-semibold dark:text-white-dark sm:grid-cols-3 sm:gap-7">
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-primary transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Primary</h5>
                                    <span className="text-white-dark">#4361ee</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-info transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Info</h5>
                                    <span className="text-white-dark">#2196f3</span>
                                </div>
                            </div>

                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-success transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Success</h5>
                                    <span className="text-white-dark">#00ab55</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-warning transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Warning</h5>
                                    <span className="text-white-dark">#e2a03f</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-danger transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Danger</h5>
                                    <span className="text-white-dark">#e7515a</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-secondary transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Secondry</h5>
                                    <span className="text-white-dark">#805dca</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-dark transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Dark</h5>
                                    <span className="text-white-dark">#3b3f5c</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-white transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">White</h5>
                                    <span className="text-white-dark">#ffffff</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-black transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Black</h5>
                                    <span className="text-white-dark">#0e1726</span>
                                </div>
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 font-semibold dark:text-white-dark sm:grid-cols-3 sm:gap-7">
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-primary-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Primary-Light</h5>
                                    <span className="text-white-dark">#eaf1ff</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-info-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Info-Light</h5>
                                    <span className="text-white-dark">#e7f7ff</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-success-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Success-Light</h5>
                                    <span className="text-white-dark">#ddf5f0</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-warning-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Warning-Light</h5>
                                    <span className="text-white-dark">#fff9ed</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-danger-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Danger-Light</h5>
                                    <span className="text-white-dark">#fff5f5</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-secondary-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Secondry-Light</h5>
                                    <span className="text-white-dark">#ebe4f7</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-dark-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Dark-Light</h5>
                                    <span className="text-white-dark">#eaeaec</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-white-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">White-Light</h5>
                                    <span className="text-white-dark">#e0e6ed</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-black-light transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <h5 className="text-sm sm:text-base">Black-Light</h5>
                                    <span className="text-white-dark">#e3e4eb</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* more */}
                <div>
                    <div className="mb-5 w-fit">
                        <h5 className="rounded bg-success/20 px-3 py-1 text-sm font-semibold text-success sm:text-base">More Colors</h5>
                    </div>
                    <div>
                        <div className="grid grid-cols-2 gap-4 font-semibold dark:text-white-dark sm:grid-cols-3 sm:gap-7">
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#e3e7fc] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#e3e7fc</span>
                                </div>
                            </div>

                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#deeffd] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#deeffd</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#d9f2e6] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#d9f2e6</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#fbf1e2] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#fbf1e2</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#fbe5e6] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#fbe5e6</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#ece7f7] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#ece7f7</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#e2e2e7] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#e2e2e7</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#888ea8] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#888ea8</span>
                                </div>
                            </div>
                            <div className="panel group flex items-center rounded-md p-2.5">
                                <div className="-m-2.5 h-[84px] w-20 bg-[#dbdcdf] transition-all duration-700 group-hover:scale-110 ltr:mr-4 ltr:rounded-l-md rtl:ml-4 rtl:rounded-r-md"></div>
                                <div>
                                    <span className="text-white-dark">#dbdcdf</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ColorLibrary;
