import IconBell from '@/components/icon/icon-bell';
import IconAirplay from '@/components/icon/icon-airplay';
import IconArchive from '@/components/icon/icon-archive';
import IconArrowBackward from '@/components/icon/icon-arrow-backward';
import IconArrowForward from '@/components/icon/icon-arrow-forward';
import Icon<PERSON>rrowLeft from '@/components/icon/icon-arrow-left';
import IconAt from '@/components/icon/icon-at';
import IconAward from '@/components/icon/icon-award';
import IconBarChart from '@/components/icon/icon-bar-chart';
import IconBellBing from '@/components/icon/icon-bell-bing';
import IconBolt from '@/components/icon/icon-bolt';
import IconBook from '@/components/icon/icon-book';
import IconBookmark from '@/components/icon/icon-bookmark';
import IconBox from '@/components/icon/icon-box';
import IconCalendar from '@/components/icon/icon-calendar';
import IconCamera from '@/components/icon/icon-camera';
import IconCaretDown from '@/components/icon/icon-caret-down';
import IconCaretsDown from '@/components/icon/icon-carets-down';
import IconCashBanknotes from '@/components/icon/icon-cash-banknotes';
import IconChartSquare from '@/components/icon/icon-chart-square';
import IconChatDot from '@/components/icon/icon-chat-dot';
import IconChatDots from '@/components/icon/icon-chat-dots';
import IconChatNotification from '@/components/icon/icon-chat-notification';
import IconChecks from '@/components/icon/icon-checks';
import IconChrome from '@/components/icon/icon-chrome';
import IconCircleCheck from '@/components/icon/icon-circle-check';
import IconClipboardText from '@/components/icon/icon-clipboard-text';
import IconClock from '@/components/icon/icon-clock';
import IconCloudDownload from '@/components/icon/icon-cloud-download';
import IconCode from '@/components/icon/icon-code';
import IconCoffee from '@/components/icon/icon-coffee';
import IconCopy from '@/components/icon/icon-copy';
import IconCpuBolt from '@/components/icon/icon-cpu-bolt';
import IconCreditCard from '@/components/icon/icon-credit-card';
import IconDesktop from '@/components/icon/icon-desktop';
import IconDollarSign from '@/components/icon/icon-dollar-sign';
import IconDollarSignCircle from '@/components/icon/icon-dollar-sign-circle';
import IconDownload from '@/components/icon/icon-download';
import IconDribbble from '@/components/icon/icon-dribbble';
import IconDroplet from '@/components/icon/icon-droplet';
import IconEdit from '@/components/icon/icon-edit';
import IconInfoCircle from '@/components/icon/icon-info-circle';
import IconEye from '@/components/icon/icon-eye';
import IconFacebook from '@/components/icon/icon-facebook';
import IconFile from '@/components/icon/icon-file';
import IconFolder from '@/components/icon/icon-folder';
import IconFolderMinus from '@/components/icon/icon-folder-minus';
import IconFolderPlus from '@/components/icon/icon-folder-plus';
import IconGallery from '@/components/icon/icon-gallery';
import IconGithub from '@/components/icon/icon-github';
import IconGlobe from '@/components/icon/icon-globe';
import IconHeart from '@/components/icon/icon-heart';
import IconHelpCircle from '@/components/icon/icon-help-circle';
import IconHome from '@/components/icon/icon-home';
import IconHorizontalDots from '@/components/icon/icon-horizontal-dots';
import IconInbox from '@/components/icon/icon-inbox';
import IconInfoHexagon from '@/components/icon/icon-info-hexagon';
import IconInfoTriangle from '@/components/icon/icon-info-triangle';
import IconInstagram from '@/components/icon/icon-instagram';
import IconLaptop from '@/components/icon/icon-laptop';
import IconLayout from '@/components/icon/icon-layout';
import IconLayoutGrid from '@/components/icon/icon-layout-grid';
import IconLink from '@/components/icon/icon-link';
import IconLinkedin from '@/components/icon/icon-linkedin';
import IconListCheck from '@/components/icon/icon-list-check';
import IconLoader from '@/components/icon/icon-loader';
import IconLock from '@/components/icon/icon-lock';
import IconLockDots from '@/components/icon/icon-lock-dots';
import IconLogin from '@/components/icon/icon-login';
import IconLogout from '@/components/icon/icon-logout';
import IconMail from '@/components/icon/icon-mail';
import IconMailDot from '@/components/icon/icon-mail-dot';
import IconMapPin from '@/components/icon/icon-map-pin';
import IconMenu from '@/components/icon/icon-menu';
import IconMessage from '@/components/icon/icon-message';
import IconMessage2 from '@/components/icon/icon-message2';
import IconMessageDots from '@/components/icon/icon-message-dots';
import IconMessagesDot from '@/components/icon/icon-messages-dot';
import IconMicrophoneOff from '@/components/icon/icon-microphone-off';
import IconMinus from '@/components/icon/icon-minus';
import IconMinusCircle from '@/components/icon/icon-minus-circle';
import IconMoodSmile from '@/components/icon/icon-mood-smile';
import IconMoon from '@/components/icon/icon-moon';
import IconMultipleForwardRight from '@/components/icon/icon-multiple-forward-right';
import IconNotes from '@/components/icon/icon-notes';
import IconNotesEdit from '@/components/icon/icon-notes-edit';
import IconOpenBook from '@/components/icon/icon-open-book';
import IconPaperclip from '@/components/icon/icon-paperclip';
import IconPencil from '@/components/icon/icon-pencil';
import IconPencilPaper from '@/components/icon/icon-pencil-paper';
import IconPhone from '@/components/icon/icon-phone';
import IconPhoneCall from '@/components/icon/icon-phone-call';
import IconPlayCircle from '@/components/icon/icon-play-circle';
import IconPlus from '@/components/icon/icon-plus';
import IconPlusCircle from '@/components/icon/icon-plus-circle';
import IconPrinter from '@/components/icon/icon-printer';
import IconRefresh from '@/components/icon/icon-refresh';
import IconRestore from '@/components/icon/icon-restore';
import IconRouter from '@/components/icon/icon-router';
import IconSafari from '@/components/icon/icon-safari';
import IconSave from '@/components/icon/icon-save';
import IconSearch from '@/components/icon/icon-search';
import IconSend from '@/components/icon/icon-send';
import IconServer from '@/components/icon/icon-server';
import IconSettings from '@/components/icon/icon-settings';
import IconShare from '@/components/icon/icon-share';
import IconShoppingBag from '@/components/icon/icon-shopping-bag';
import IconShoppingCart from '@/components/icon/icon-shopping-cart';
import IconSquareCheck from '@/components/icon/icon-square-check';
import IconSquareRotated from '@/components/icon/icon-square-rotated';
import IconStar from '@/components/icon/icon-star';
import IconSun from '@/components/icon/icon-sun';
import IconTag from '@/components/icon/icon-tag';
import IconThumbUp from '@/components/icon/icon-thumb-up';
import IconTrash from '@/components/icon/icon-trash';
import IconTrashLines from '@/components/icon/icon-trash-lines';
import IconTrendingUp from '@/components/icon/icon-trending-up';
import IconTwitter from '@/components/icon/icon-twitter';
import IconUser from '@/components/icon/icon-user';
import IconUserPlus from '@/components/icon/icon-user-plus';
import IconUsers from '@/components/icon/icon-users';
import IconUsersGroup from '@/components/icon/icon-users-group';
import IconVideo from '@/components/icon/icon-video';
import IconWheel from '@/components/icon/icon-wheel';
import IconX from '@/components/icon/icon-x';
import IconXCircle from '@/components/icon/icon-x-circle';
import IconZipFile from '@/components/icon/icon-zip-file';
import IconMenuApps from '@/components/icon/menu/icon-menu-apps';
import IconMenuAuthentication from '@/components/icon/menu/icon-menu-authentication';
import IconMenuCalendar from '@/components/icon/menu/icon-menu-calendar';
import IconMenuCharts from '@/components/icon/menu/icon-menu-charts';
import IconMenuChat from '@/components/icon/menu/icon-menu-chat';
import IconMenuComponents from '@/components/icon/menu/icon-menu-components';
import IconMenuContacts from '@/components/icon/menu/icon-menu-contacts';
import IconMenuDashboard from '@/components/icon/menu/icon-menu-dashboard';
import IconMenuDatatables from '@/components/icon/menu/icon-menu-datatables';
import IconMenuDocumentation from '@/components/icon/menu/icon-menu-documentation';
import IconMenuDragAndDrop from '@/components/icon/menu/icon-menu-drag-and-drop';
import IconMenuElements from '@/components/icon/menu/icon-menu-elements';
import IconMenuFontIcons from '@/components/icon/menu/icon-menu-font-icons';
import IconMenuForms from '@/components/icon/menu/icon-menu-forms';
import IconMenuInvoice from '@/components/icon/menu/icon-menu-invoice';
import IconMenuMailbox from '@/components/icon/menu/icon-menu-mailbox';
import IconMenuMore from '@/components/icon/menu/icon-menu-more';
import IconMenuNotes from '@/components/icon/menu/icon-menu-notes';
import IconMenuPages from '@/components/icon/menu/icon-menu-pages';
import IconMenuScrumboard from '@/components/icon/menu/icon-menu-scrumboard';
import IconMenuTables from '@/components/icon/menu/icon-menu-tables';
import IconMenuTodo from '@/components/icon/menu/icon-menu-todo';
import IconMenuUsers from '@/components/icon/menu/icon-menu-users';
import IconMenuWidgets from '@/components/icon/menu/icon-menu-widgets';
import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Font Icons',
};

const FontIcons = () => {
    return (
        <div>
            <div className="space-y-8">
                <div className="panel flex items-center overflow-x-auto whitespace-nowrap p-3 text-primary lg:col-span-2">
                    <div className="rounded-full bg-primary p-1.5 text-white ring-2 ring-primary/30 ltr:mr-3 rtl:ml-3">
                        <IconBell />
                    </div>
                    <span className="ltr:mr-3 rtl:ml-3">Documentation: </span>
                    <a
                        href="https://www.figma.com/file/agsPUbJSO4OcokUIxJRZvw/Solar-Icon-Set-(Community)?node-id=0%3A1&t=Xr5s4CqZbVgAQU9X-0"
                        target="_blank"
                        className="block hover:underline"
                        rel="noreferrer"
                    >
                        https://www.figma.com/file/agsPUbJSO4OcokUIxJRZvw/Solar-Icon-Set-(Community)?node-id=0%3A1&t=Xr5s4CqZbVgAQU9X-0
                    </a>
                </div>

                <div className="panel">
                    <h5 className="mb-5 text-lg font-semibold dark:text-white-light">Solar Icon</h5>
                    <div className="mb-5">
                        <p className="mb-5">
                            Solar is a collection of simply beautiful open source icons. Each icon is designed on a 24x24 grid with an emphasis on simplicity, consistency and usability.
                        </p>
                        <div className="mb-5 inline-block rounded bg-[#009688]/[.26] px-2 py-1 text-base text-[#009688]">Line Duotone</div>
                        <div className="mb-5 flex flex-wrap items-center gap-10">
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconAirplay className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconArchive className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconArrowBackward className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconArrowForward className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconArrowLeft className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconAt className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconAward className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBarChart className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBell className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBellBing className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBolt className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBook className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBookmark className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBox className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCalendar className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCamera className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCaretDown className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCaretsDown className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCashBanknotes className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChartSquare className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChatDot className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChatDots className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChatNotification className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChecks className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconChrome className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCircleCheck className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconClipboardText className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconClock className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCloudDownload className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCode className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCoffee className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCopy className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCpuBolt className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconCreditCard className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDesktop className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDollarSign className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDollarSignCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDownload className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDribbble className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDroplet className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconEdit className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInfoCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconEye className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconFacebook className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconFile className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconFolder className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconFolderMinus className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconFolderPlus className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconGallery className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconGithub className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconGlobe className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconHeart className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconHelpCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconHome className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconHorizontalDots className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInbox className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInfoHexagon className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInfoTriangle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInstagram className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLaptop className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLayout className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLayoutGrid className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLink className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLinkedin className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconListCheck className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLoader className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLock className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLockDots className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLogin className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLogout className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMail className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMailDot className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMapPin className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenu className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMessage className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMessage2 className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMessageDots className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMessagesDot className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMicrophoneOff className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMinus className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMinusCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMoodSmile className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMoon className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMultipleForwardRight className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconNotes className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconNotesEdit className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconOpenBook className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPaperclip className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPencil className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPencilPaper className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPhone className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPhoneCall className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPlayCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPlus className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPlusCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPrinter className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconRefresh className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconRestore className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconRouter className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSafari className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSave className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSearch className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSend className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconServer className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSettings className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconShare className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconShoppingBag className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconShoppingCart className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSquareCheck className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSquareRotated className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconStar className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconSun className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTag className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconThumbUp className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTrash className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTrashLines className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTrendingUp className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTwitter className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconUser className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconUserPlus className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconUsers className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconUsersGroup className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconVideo className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconWheel className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconX className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconXCircle className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconZipFile className="h-6 w-6" />
                            </div>
                        </div>
                        <div className="mb-5 inline-block rounded bg-[#009688]/[.26] px-2 py-1 text-base text-[#009688]">Bold Duotone</div>
                        <div className="mb-5 flex flex-wrap items-center gap-10">
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuApps className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuAuthentication className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuCalendar className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuCharts className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuChat className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuComponents className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuContacts className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuDashboard className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuDatatables className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuDocumentation className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuDragAndDrop className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuElements className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuFontIcons className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuForms className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuInvoice className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuMailbox className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuMore className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuNotes className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuPages className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuScrumboard className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuTables className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuTodo className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuUsers className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMenuWidgets className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconAirplay fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconBox fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDesktop fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconDollarSignCircle fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconInfoCircle fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLayout fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconLockDots fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMail fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMessageDots fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconMinusCircle fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPencil fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPhoneCall fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconPlayCircle fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconRouter fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconTwitter fill={true} className="h-6 w-6" />
                            </div>
                            <div className="grid h-14 w-14 place-content-center rounded-md border border-white-dark/20 dark:border-[#191e3a]">
                                <IconUser fill={true} className="h-6 w-6" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FontIcons;
