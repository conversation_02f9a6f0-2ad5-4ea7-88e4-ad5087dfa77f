"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import IconLayoutGrid from "@/components/icon/icon-layout-grid";
import IconListCheck from "@/components/icon/icon-list-check";
import IconSearch from "@/components/icon/icon-search";
import IconFilter from "@/components/icon/icon-filter";
import IconEye from "@/components/icon/icon-eye";
import IconEdit from "@/components/icon/icon-edit";
import IconTrash from "@/components/icon/icon-trash";

// Types for better type safety
interface Campaign {
  id: number;
  name: string;
  description?: string;
  image?: string;
  status: 'draft' | 'published' | 'active' | 'paused' | 'completed';
  budget: number;
  startDate: string;
  endDate: string;
  createdAt: string;
  applicationStatus?: 'not_applied' | 'pending_sp_review' | 'pending_advertiser_review' | 'approved' | 'rejected';
}

const sampleCampaigns: Campaign[] = [
  {
    id: 1,
    name: "Summer Brand Awareness Campaign",
    description: "Promote our summer collection through engaging TikTok content",
    image: "/images/logo-light.png",
    status: "active",
    budget: 1000,
    startDate: "2024-07-01",
    endDate: "2024-08-31",
    createdAt: "2024-06-15",
    applicationStatus: "not_applied",
  },
  {
    id: 2,
    name: "Back to School Promotion",
    description: "Target students with back-to-school content",
    image: "/images/logo-light.png",
    status: "published",
    budget: 500,
    startDate: "2024-08-01",
    endDate: "2024-09-15",
    createdAt: "2024-07-01",
    applicationStatus: "pending_sp_review",
  },
  {
    id: 3,
    name: "Holiday Special Campaign",
    description: "Create festive content for the holiday season",
    image: "/images/logo-light.png",
    status: "draft",
    budget: 2000,
    startDate: "2024-12-01",
    endDate: "2024-12-31",
    createdAt: "2024-11-01",
    applicationStatus: "approved",
  },
];

// Utility functions
const getStatusColor = (status: Campaign['status']) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'published':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'paused':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'completed':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    case 'draft':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const getApplicationStatusColor = (status: Campaign['applicationStatus']) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'pending_sp_review':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'pending_advertiser_review':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'rejected':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'not_applied':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

function GridView({ data, onViewDetails, onApply }: {
  data: Campaign[];
  onViewDetails: (id: number) => void;
  onApply: (id: number) => void;
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-6 transition-all duration-300">
      {data.map((campaign) => (
        <div key={campaign.id} className="panel flex flex-col p-6 shadow-lg rounded-lg bg-white hover:shadow-2xl transition-all duration-300 border border-gray-100">
          {/* Campaign Image */}
          <div className="flex justify-center mb-4">
            <img
              src={campaign.image || "/images/logo-light.png"}
              alt={campaign.name}
              className="w-16 h-16 object-contain rounded-full border-2 border-gray-200"
            />
          </div>

          {/* Campaign Name */}
          <h3 className="font-semibold text-lg mb-2 text-center line-clamp-2">{campaign.name}</h3>

          {/* Campaign Description */}
          {campaign.description && (
            <p className="text-sm text-gray-600 mb-3 text-center line-clamp-2">{campaign.description}</p>
          )}

          {/* Status Badges */}
          <div className="flex flex-col gap-2 mb-4">
            <div className="flex justify-center">
              <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(campaign.status)}`}>
                {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
              </span>
            </div>
            {campaign.applicationStatus && (
              <div className="flex justify-center">
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold border ${getApplicationStatusColor(campaign.applicationStatus)}`}>
                  {campaign.applicationStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              </div>
            )}
          </div>

          {/* Campaign Details */}
          <div className="flex-1 space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Budget:</span>
              <span className="font-semibold">{formatCurrency(campaign.budget)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Start:</span>
              <span>{formatDate(campaign.startDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">End:</span>
              <span>{formatDate(campaign.endDate)}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={() => onViewDetails(campaign.id)}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-1"
            >
              <IconEye className="w-4 h-4" />
              View
            </button>
            {campaign.applicationStatus === 'not_applied' && campaign.status === 'active' && (
              <button
                onClick={() => onApply(campaign.id)}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              >
                Apply
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

function ListView({ data, onViewDetails, onApply }: {
  data: Campaign[];
  onViewDetails: (id: number) => void;
  onApply: (id: number) => void;
}) {
  return (
    <div className="overflow-x-auto mt-6 transition-all duration-300">
      <table className="min-w-full bg-white rounded-lg shadow-lg">
        <thead>
          <tr className="bg-gray-100 text-left">
            <th className="p-4 font-semibold text-gray-700">Campaign</th>
            <th className="p-4 font-semibold text-gray-700">Status</th>
            <th className="p-4 font-semibold text-gray-700">Application</th>
            <th className="p-4 font-semibold text-gray-700">Budget</th>
            <th className="p-4 font-semibold text-gray-700">Duration</th>
            <th className="p-4 font-semibold text-gray-700">Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map((campaign) => (
            <tr key={campaign.id} className="border-b last:border-none hover:bg-gray-50 transition-colors duration-200">
              {/* Campaign Info */}
              <td className="p-4">
                <div className="flex items-center gap-3">
                  <img
                    src={campaign.image || "/images/logo-light.png"}
                    alt={campaign.name}
                    className="w-10 h-10 object-contain rounded-full border border-gray-200"
                  />
                  <div>
                    <div className="font-semibold text-gray-900">{campaign.name}</div>
                    {campaign.description && (
                      <div className="text-sm text-gray-600 line-clamp-1">{campaign.description}</div>
                    )}
                  </div>
                </div>
              </td>

              {/* Campaign Status */}
              <td className="p-4">
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold border ${getStatusColor(campaign.status)}`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>
              </td>

              {/* Application Status */}
              <td className="p-4">
                {campaign.applicationStatus && (
                  <span className={`inline-block px-3 py-1 rounded-full text-xs font-semibold border ${getApplicationStatusColor(campaign.applicationStatus)}`}>
                    {campaign.applicationStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                )}
              </td>

              {/* Budget */}
              <td className="p-4 font-semibold">{formatCurrency(campaign.budget)}</td>

              {/* Duration */}
              <td className="p-4">
                <div className="text-sm">
                  <div>{formatDate(campaign.startDate)}</div>
                  <div className="text-gray-600">to {formatDate(campaign.endDate)}</div>
                </div>
              </td>

              {/* Actions */}
              <td className="p-4">
                <div className="flex gap-2">
                  <button
                    onClick={() => onViewDetails(campaign.id)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200 flex items-center gap-1"
                  >
                    <IconEye className="w-4 h-4" />
                    View
                  </button>
                  {campaign.applicationStatus === 'not_applied' && campaign.status === 'active' && (
                    <button
                      onClick={() => onApply(campaign.id)}
                      className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
                    >
                      Apply
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

const PublisherCampaignListPage = () => {
  const router = useRouter();
  const [campaigns, setCampaigns] = useState<Campaign[]>(sampleCampaigns);
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>(sampleCampaigns);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<'grid' | 'list'>("grid");

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<Campaign['status'] | 'all'>('all');
  const [applicationStatusFilter, setApplicationStatusFilter] = useState<Campaign['applicationStatus'] | 'all'>('all');

  // Fetch campaigns from API
  useEffect(() => {
    const fetchCampaigns = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual API call
        // const response = await fetch('/api/campaigns', {
        //   headers: {
        //     'Authorization': `Bearer ${localStorage.getItem('token')}`,
        //   },
        // });
        // if (!response.ok) throw new Error('Failed to fetch campaigns');
        // const data = await response.json();
        // setCampaigns(data);

        // For now, use sample data
        setCampaigns(sampleCampaigns);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  // Filter campaigns based on search and filters
  useEffect(() => {
    let filtered = campaigns;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(campaign =>
        campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (campaign.description && campaign.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(campaign => campaign.status === statusFilter);
    }

    // Apply application status filter
    if (applicationStatusFilter !== 'all') {
      filtered = filtered.filter(campaign => campaign.applicationStatus === applicationStatusFilter);
    }

    setFilteredCampaigns(filtered);
  }, [campaigns, searchTerm, statusFilter, applicationStatusFilter]);

  // Handle actions
  const handleViewDetails = (campaignId: number) => {
    router.push(`/publisher/campaign-detail/${campaignId}`);
  };

  const handleApply = async (campaignId: number) => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/campaigns/${campaignId}/apply`, {
      //   method: 'POST',
      //   headers: {
      //     'Authorization': `Bearer ${localStorage.getItem('token')}`,
      //     'Content-Type': 'application/json',
      //   },
      // });
      // if (!response.ok) throw new Error('Failed to apply to campaign');

      // Update local state to reflect application
      setCampaigns(prev => prev.map(campaign =>
        campaign.id === campaignId
          ? { ...campaign, applicationStatus: 'pending_sp_review' as const }
          : campaign
      ));

      alert('Application submitted successfully!');
    } catch (err) {
      alert('Failed to apply to campaign. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading campaigns...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading campaigns</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="panel p-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Campaign List</h1>
            <p className="text-gray-600 mt-1">
              Browse and apply to available campaigns ({filteredCampaigns.length} of {campaigns.length})
            </p>
          </div>

          {/* View Toggle */}
          <div className="flex gap-2 items-center">
            <button
              aria-label="Grid view"
              className={`p-2 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 ${
                view === "grid"
                  ? "bg-blue-600 text-white border-blue-600 shadow-md"
                  : "bg-white text-gray-600 border-gray-200 hover:bg-gray-50"
              }`}
              onClick={() => setView("grid")}
            >
              <IconLayoutGrid className="w-5 h-5" />
            </button>
            <button
              aria-label="List view"
              className={`p-2 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 ${
                view === "list"
                  ? "bg-blue-600 text-white border-blue-600 shadow-md"
                  : "bg-white text-gray-600 border-gray-200 hover:bg-gray-50"
              }`}
              onClick={() => setView("list")}
            >
              <IconListCheck className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search campaigns..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as Campaign['status'] | 'all')}
                className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white"
              >
                <option value="all">All Statuses</option>
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="active">Active</option>
                <option value="paused">Paused</option>
                <option value="completed">Completed</option>
              </select>
              <IconFilter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
            </div>

            {/* Application Status Filter */}
            <div className="relative">
              <select
                value={applicationStatusFilter}
                onChange={(e) => setApplicationStatusFilter(e.target.value as Campaign['applicationStatus'] | 'all')}
                className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white"
              >
                <option value="all">All Applications</option>
                <option value="not_applied">Not Applied</option>
                <option value="pending_sp_review">Pending SP Review</option>
                <option value="pending_advertiser_review">Pending Advertiser Review</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              <IconFilter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* Campaign List */}
        {filteredCampaigns.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' || applicationStatusFilter !== 'all'
                ? "Try adjusting your search or filter criteria."
                : "There are no campaigns available at the moment."}
            </p>
          </div>
        ) : (
          <div className="transition-all duration-300">
            {view === "grid" ? (
              <GridView
                data={filteredCampaigns}
                onViewDetails={handleViewDetails}
                onApply={handleApply}
              />
            ) : (
              <ListView
                data={filteredCampaigns}
                onViewDetails={handleViewDetails}
                onApply={handleApply}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PublisherCampaignListPage;
