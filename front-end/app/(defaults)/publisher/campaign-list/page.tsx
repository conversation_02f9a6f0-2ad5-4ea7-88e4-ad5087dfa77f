"use client";
import React, { useState, useEffect } from "react";
import IconLayoutGrid from "@/components/icon/icon-layout-grid";
import IconListCheck from "@/components/icon/icon-list-check";

const sampleCampaigns = [
  {
    id: 1,
    name: "Campaign A",
    image: "/images/logo-light.png",
    status: "Active",
    budget: "$1,000",
    date: "2024-07-01",
  },
  {
    id: 2,
    name: "Campaign B",
    image: "/images/logo-light.png",
    status: "Paused",
    budget: "$500",
    date: "2024-07-02",
  },
];

function GridView({ data }: { data: typeof sampleCampaigns }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mt-6 transition-all duration-300">
      {data.map((c) => (
        <div key={c.id} className="panel flex flex-col items-center p-6 shadow-lg rounded-lg bg-white hover:shadow-2xl transition-shadow duration-300">
          <img src={c.image} alt={c.name} className="w-20 h-20 object-contain mb-2 rounded-full border-2 border-gray-200" />
          <h3 className="font-semibold text-lg mb-1">{c.name}</h3>
          <div className="text-sm mb-2">
            <span className={`inline-block px-2 py-0.5 rounded text-xs font-semibold ${c.status === 'Active' ? 'bg-green-100 text-green-700' : c.status === 'Paused' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'}`}>{c.status}</span>
          </div>
          <div className="flex justify-between w-full text-sm mt-2">
            <span>Budget: <b>{c.budget}</b></span>
            <span>{c.date}</span>
          </div>
        </div>
      ))}
    </div>
  );
}

function ListView({ data }: { data: typeof sampleCampaigns }) {
  return (
    <div className="overflow-x-auto mt-6 transition-all duration-300">
      <table className="min-w-full bg-white rounded-lg shadow-lg">
        <thead>
          <tr className="bg-gray-100 text-left">
            <th className="p-3">#</th>
            <th className="p-3">Name</th>
            <th className="p-3">Status</th>
            <th className="p-3">Budget</th>
            <th className="p-3">Date</th>
          </tr>
        </thead>
        <tbody>
          {data.map((c) => (
            <tr key={c.id} className="border-b last:border-none hover:bg-gray-50 transition-colors duration-200">
              <td className="p-3">{c.id}</td>
              <td className="p-3 flex items-center gap-2">
                <img src={c.image} alt={c.name} className="w-8 h-8 object-contain rounded-full border border-gray-200" />
                {c.name}
              </td>
              <td className="p-3">
                <span className={`inline-block px-2 py-0.5 rounded text-xs font-semibold ${c.status === 'Active' ? 'bg-green-100 text-green-700' : c.status === 'Paused' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'}`}>{c.status}</span>
              </td>
              <td className="p-3">{c.budget}</td>
              <td className="p-3">{c.date}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

const PublisherCampaignListPage = () => {
  const [campaigns, setCampaigns] = useState(sampleCampaigns);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<'grid' | 'list'>("grid");

  // useEffect(() => {
  //   setLoading(true);
  //   fetch('/api/campaigns')
  //     .then(res => res.json())
  //     .then(data => { setCampaigns(data); setLoading(false); })
  //     .catch(err => { setError(err.message); setLoading(false); });
  // }, []);

  if (loading) return <div className="p-6">Loading...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!campaigns.length) return <div className="p-6">No campaigns found.</div>;

  return (
    <div className="p-6">
      <div className="panel p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h1 className="text-2xl font-bold mb-2 md:mb-0">Campaign List</h1>
          <div className="flex gap-2 items-center">
            {/* Search/filter controls can be added here */}
            <button
              aria-label="Grid view"
              className={`p-2 rounded-full border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 ${view === "grid" ? "bg-blue-600 text-white border-blue-600 shadow" : "bg-gray-100 text-gray-600 border-gray-200"}`}
              onClick={() => setView("grid")}
            >
              <IconLayoutGrid className="w-5 h-5" />
            </button>
            <button
              aria-label="List view"
              className={`p-2 rounded-full border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 ${view === "list" ? "bg-blue-600 text-white border-blue-600 shadow" : "bg-gray-100 text-gray-600 border-gray-200"}`}
              onClick={() => setView("list")}
            >
              <IconListCheck className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="transition-all duration-300">
          {view === "grid" ? <GridView data={campaigns} /> : <ListView data={campaigns} />}
        </div>
      </div>
    </div>
  );
};

export default PublisherCampaignListPage;
