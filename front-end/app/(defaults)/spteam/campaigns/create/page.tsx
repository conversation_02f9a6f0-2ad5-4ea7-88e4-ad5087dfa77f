// front-end/app/(defaults)/spteam/campaigns/create/page.tsx

"use client";

import React, { useState } from "react";

const CreateCampaignPage = () => {
  const [form, setForm] = useState({
    name: "",
    description: "",
    budget: "",
    startDate: "",
    endDate: "",
  });
  const [status, setStatus] = useState<null | "success" | "error">(null);
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setStatus(null);

    // Stubbed API call
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setStatus("success");
      setForm({
        name: "",
        description: "",
        budget: "",
        startDate: "",
        endDate: "",
      });
    } catch {
      setStatus("error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: 500, margin: "40px auto", padding: 24, border: "1px solid #eee", borderRadius: 8 }}>
      <h2>Create Campaign</h2>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: 16 }}>
          <label>
            Campaign Name
            <input
              type="text"
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              style={{ width: "100%", padding: 8, marginTop: 4 }}
            />
          </label>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>
            Description
            <textarea
              name="description"
              value={form.description}
              onChange={handleChange}
              required
              style={{ width: "100%", padding: 8, marginTop: 4, minHeight: 80 }}
            />
          </label>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>
            Budget
            <input
              type="number"
              name="budget"
              value={form.budget}
              onChange={handleChange}
              required
              min="0"
              step="0.01"
              style={{ width: "100%", padding: 8, marginTop: 4 }}
            />
          </label>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>
            Start Date
            <input
              type="date"
              name="startDate"
              value={form.startDate}
              onChange={handleChange}
              required
              style={{ width: "100%", padding: 8, marginTop: 4 }}
            />
          </label>
        </div>
        <div style={{ marginBottom: 16 }}>
          <label>
            End Date
            <input
              type="date"
              name="endDate"
              value={form.endDate}
              onChange={handleChange}
              required
              style={{ width: "100%", padding: 8, marginTop: 4 }}
            />
          </label>
        </div>
        <button type="submit" disabled={loading} style={{ padding: "10px 24px" }}>
          {loading ? "Submitting..." : "Create Campaign"}
        </button>
      </form>
      {status === "success" && (
        <div style={{ color: "green", marginTop: 16 }}>Campaign created successfully.</div>
      )}
      {status === "error" && (
        <div style={{ color: "red", marginTop: 16 }}>Failed to create campaign. Please try again.</div>
      )}
    </div>
  );
};

export default CreateCampaignPage;