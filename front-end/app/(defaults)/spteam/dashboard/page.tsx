"use client";
import React from "react";
import { useSelector } from "react-redux";
import { selectAuth } from "@/store/authSlice";
import { useRouter } from "next/navigation";

const SPTeamDashboard = () => {
  const router = useRouter();
  const { user } = useSelector(selectAuth);

  // Stat data (có thể fetch từ API nếu backend có)
  const stats = [
    {
      label: "Applications",
      value: 128,
      sub: "Pending Reviews",
      icon: <span className="text-green-700 text-2xl">👥</span>,
    },
    {
      label: "Campaigns",
      value: 42,
      sub: "Active Campaigns",
      icon: <span className="text-green-700 text-2xl">📊</span>,
    },
    {
      label: "Moderations",
      value: 17,
      sub: "Content to Review",
      icon: <span className="text-green-700 text-2xl">📝</span>,
    },
    {
      label: "Reports",
      value: 8,
      sub: "New Analytics",
      icon: <span className="text-green-700 text-2xl">📈</span>,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-2xl font-bold mb-2">Platform Administration Center</h2>
          <p className="text-gray-600">
            Oversee platform operations, manage user applications, monitor campaign quality, and ensure compliance across all brand-creator collaborations.
          </p>
        </div>
        {/* Stat Widgets */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat) => (
            <div key={stat.label} className="panel flex flex-col items-center p-6">
              <div className="mb-2">{stat.icon}</div>
              <div className="text-lg font-semibold">{stat.value}</div>
              <div className="text-gray-500">{stat.label}</div>
              <div className="text-xs text-gray-400 mt-1">{stat.sub}</div>
            </div>
          ))}
        </div>
        {/* Chart and Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="panel col-span-2 p-6 flex flex-col">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Platform Performance</h3>
              <button className="bg-green-700 text-white px-3 py-1 rounded">Filter</button>
            </div>
            {/* Chart placeholder */}
            <div className="flex items-center justify-center h-64 text-gray-400 text-4xl">📈 Chart goes here</div>
          </div>
          <div className="flex flex-col gap-6">
            <div className="panel p-6">
              <div className="flex items-center gap-4">
                <div className="rounded bg-green-100 p-2 text-green-700 text-2xl">🏢</div>
                <div>
                  <h4 className="font-semibold">Quick Actions</h4>
                  <p className="text-xs text-gray-500">Manage platform tasks</p>
                </div>
              </div>
              <div className="mt-4 flex flex-col gap-2">
                <button className="w-full py-2 px-4 bg-green-700 text-white rounded hover:bg-green-800 font-medium transition">Review Applications</button>
                <button className="w-full py-2 px-4 border border-green-700 text-green-700 rounded hover:bg-green-50 font-medium transition">View Campaigns</button>
                <button className="w-full py-2 px-4 border border-green-700 text-green-700 rounded hover:bg-green-50 font-medium transition">Review Content</button>
                <button className="w-full py-2 px-4 border border-green-700 text-green-700 rounded hover:bg-green-50 font-medium transition">View Analytics</button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SPTeamDashboard;
