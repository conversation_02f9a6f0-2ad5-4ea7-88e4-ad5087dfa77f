'use client';
import AuthGuard from '@/components/layouts/AuthGuard';
import ContentAnimation from '@/components/layouts/content-animation';
import Footer from '@/components/layouts/footer';
import Header from '@/components/layouts/header';
import MainContainer from '@/components/layouts/main-container';
import Overlay from '@/components/layouts/overlay';
import ScrollToTop from '@/components/layouts/scroll-to-top';
import Setting from '@/components/layouts/setting';
import Sidebar from '@/components/layouts/sidebar';
import Portals from '@/components/portals';
import { useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import PublisherDashboard from './publisher/dashboard/page';
import AdvertiserDashboard from './advertiser/dashboard/page';
import SPTeamDashboard from './spteam/dashboard/page';
import { usePathname } from 'next/navigation';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const { user } = useSelector((state: any) => state.auth);
    const [showDashboard, setShowDashboard] = useState(false);
    const pathname = usePathname();

    useEffect(() => {
        // Nếu là route mặc định (/) hoặc (defaults), tự động show dashboard
        if (pathname === '/' || pathname === '/(defaults)') {
            setShowDashboard(true);
        } else {
            setShowDashboard(false);
        }
    }, [pathname]);

    const renderDashboard = () => {
        if (!user) return null;
        if (user.role === 'publisher') return <PublisherDashboard />;
        if (user.role === 'advertiser') return <AdvertiserDashboard />;
        if (user.role === 'sp_team' || user.role === 'staff') return <SPTeamDashboard />;
        return null;
    };

    return (
        <AuthGuard>
            {/* BEGIN MAIN CONTAINER */}
            <div className="relative">
                <Overlay />
                <ScrollToTop />

                {/* BEGIN APP SETTING LAUNCHER */}
                <Setting />
                {/* END APP SETTING LAUNCHER */}

                <MainContainer>
                    {/* BEGIN SIDEBAR */}
                    <Sidebar onDashboardClick={() => setShowDashboard(true)} />
                    {/* END SIDEBAR */}
                    <div className="main-content flex min-h-screen flex-col">
                        {/* BEGIN TOP NAVBAR */}
                        <Header />
                        {/* END TOP NAVBAR */}

                        {/* BEGIN CONTENT AREA */}
                        <ContentAnimation>
                            {showDashboard ? renderDashboard() : children}
                        </ContentAnimation>
                        {/* END CONTENT AREA */}

                        {/* BEGIN FOOTER */}
                        <Footer />
                        {/* END FOOTER */}
                        <Portals />
                    </div>
                </MainContainer>
            </div>
        </AuthGuard>
    );
}
