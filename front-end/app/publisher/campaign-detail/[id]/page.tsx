"use client";
import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';

// Dữ liệu mẫu cho campaign
const sampleCampaign: any = {
  id: 1,
  title: "Campaign A",
  description: "This is a sample campaign for publishers to join and earn.",
  status: "Active",
  budget: 1000,
  requirements: "Must have at least 10k followers on TikTok.",
};

const getCampaignDetail = async (id: string) => {
  // Thay bằng fetch API nếu có
  return new Promise<any>((resolve) => setTimeout(() => resolve(sampleCampaign), 500));
};

const applyCampaign = async (id: string) => {
  // Thay bằng fetch API nếu có
  return new Promise<void>((resolve) => setTimeout(() => resolve(), 500));
};

export default function PublisherCampaignDetailPage() {
  const params = useParams();
  const id = params.id as string;
  const [campaign, setCampaign] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [applied, setApplied] = useState(false);
  const [applying, setApplying] = useState(false);

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    getCampaignDetail(id)
      .then((data) => {
        setCampaign(data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message || 'Error fetching campaign');
        setLoading(false);
      });
  }, [id]);

  const handleApply = async () => {
    setApplying(true);
    try {
      await applyCampaign(id);
      setApplied(true);
    } catch (err) {
      alert('Apply failed!');
    } finally {
      setApplying(false);
    }
  };

  if (loading) return <div className="p-6">Loading...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!campaign) return <div className="p-6">Campaign not found.</div>;

  return (
    <div className="max-w-2xl mx-auto mt-10">
      <div className="panel p-8 shadow-lg rounded-lg bg-white dark:bg-black">
        <h1 className="text-2xl font-bold mb-4 text-primary">Campaign Detail</h1>
        <h2 className="text-xl font-semibold mb-2">{campaign.title}</h2>
        <p className="mb-2 text-gray-700">{campaign.description}</p>
        <div className="mb-2 flex gap-4">
          <span className="px-3 py-1 rounded bg-blue-100 text-blue-700 font-semibold">Status: {campaign.status}</span>
          <span className="px-3 py-1 rounded bg-green-100 text-green-700 font-semibold">Budget: ${campaign.budget}</span>
        </div>
        <div className="mb-4">
          <span className="font-semibold">Requirements:</span> {campaign.requirements}
        </div>
        {applied ? (
          <div className="text-green-600 font-semibold text-center">✅ Applied successfully!</div>
        ) : (
          <button
            className="btn btn-gradient w-full uppercase font-semibold mt-4"
            onClick={handleApply}
            disabled={applying}
          >
            {applying ? "Applying..." : "Apply to Campaign"}
          </button>
        )}
      </div>
    </div>
  );
}
