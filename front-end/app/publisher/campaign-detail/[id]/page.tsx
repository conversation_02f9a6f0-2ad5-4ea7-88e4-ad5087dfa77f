"use client";
import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import IconCalendar from '@/components/icon/icon-calendar';
import IconDollarSign from '@/components/icon/icon-dollar-sign';
import IconUsers from '@/components/icon/icon-users';
import IconClock from '@/components/icon/icon-clock';
import IconArrowLeft from '@/components/icon/icon-arrow-left';
import IconEye from '@/components/icon/icon-eye';
import IconVideo from '@/components/icon/icon-video';

// Types for better type safety
interface Campaign {
  id: number;
  name: string;
  description: string;
  image?: string;
  status: 'draft' | 'published' | 'active' | 'paused' | 'completed';
  budget: number;
  startDate: string;
  endDate: string;
  createdAt: string;
  requirements: string;
  targetAudience: string;
  contentGuidelines: string;
  deliverables: string[];
  applicationDeadline: string;
  applicationStatus?: 'not_applied' | 'pending_sp_review' | 'pending_advertiser_review' | 'approved' | 'rejected';
  rejectionReason?: string;
  advertiser: {
    name: string;
    logo?: string;
    description: string;
  };
  metrics: {
    totalApplications: number;
    approvedApplications: number;
    completedVideos: number;
  };
}

// Sample campaign data with enhanced structure
const sampleCampaign: Campaign = {
  id: 1,
  name: "Summer Brand Awareness Campaign",
  description: "Join our exciting summer campaign to promote our latest product line through engaging TikTok content. We're looking for creative publishers who can showcase our products in authentic and entertaining ways.",
  image: "/images/logo-light.png",
  status: "active",
  budget: 1000,
  startDate: "2024-07-01",
  endDate: "2024-08-31",
  createdAt: "2024-06-15",
  requirements: "Must have at least 10,000 followers on TikTok, active engagement rate above 3%, and content aligned with our brand values.",
  targetAudience: "Young adults aged 18-35 interested in lifestyle, fashion, and technology products.",
  contentGuidelines: "Videos should be 15-60 seconds long, include our product naturally in the content, use trending sounds when appropriate, and maintain a positive, energetic tone.",
  deliverables: [
    "1 TikTok video featuring our product",
    "Include specific hashtags: #SummerVibes #BrandName #Partnership",
    "Tag our official account @brandname",
    "Submit video for review before posting",
    "Provide final public link after posting"
  ],
  applicationDeadline: "2024-07-15",
  applicationStatus: "not_applied",
  advertiser: {
    name: "TechStyle Co.",
    logo: "/images/logo-light.png",
    description: "Leading lifestyle and technology brand focused on innovative products for modern consumers."
  },
  metrics: {
    totalApplications: 45,
    approvedApplications: 12,
    completedVideos: 8
  }
};

// Utility functions
const getStatusColor = (status: Campaign['status']) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'published':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'paused':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'completed':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    case 'draft':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const getApplicationStatusColor = (status: Campaign['applicationStatus']) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'pending_sp_review':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'pending_advertiser_review':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'rejected':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'not_applied':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const isDeadlinePassed = (deadline: string) => {
  return new Date(deadline) < new Date();
};

// API functions
const getCampaignDetail = async (id: string): Promise<Campaign> => {
  try {
    // TODO: Replace with actual API call
    // const response = await fetch(`/api/campaigns/${id}`, {
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`,
    //   },
    // });
    // if (!response.ok) throw new Error('Failed to fetch campaign');
    // return await response.json();

    // For now, return sample data
    return new Promise((resolve) => setTimeout(() => resolve(sampleCampaign), 500));
  } catch (error) {
    throw new Error('Failed to fetch campaign details');
  }
};

const applyCampaign = async (id: string): Promise<void> => {
  try {
    // TODO: Replace with actual API call
    // const response = await fetch(`/api/campaigns/${id}/apply`, {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`,
    //     'Content-Type': 'application/json',
    //   },
    // });
    // if (!response.ok) throw new Error('Failed to apply to campaign');

    // For now, simulate API call
    return new Promise((resolve) => setTimeout(() => resolve(), 1000));
  } catch (error) {
    throw new Error('Failed to apply to campaign');
  }
};

export default function PublisherCampaignDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [applying, setApplying] = useState(false);

  useEffect(() => {
    if (!id) return;

    const fetchCampaign = async () => {
      setLoading(true);
      try {
        const data = await getCampaignDetail(id);
        setCampaign(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error fetching campaign');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [id]);

  const handleApply = async () => {
    if (!campaign) return;

    setApplying(true);
    try {
      await applyCampaign(id);

      // Update local state to reflect application
      setCampaign(prev => prev ? {
        ...prev,
        applicationStatus: 'pending_sp_review'
      } : null);

      alert('Application submitted successfully! You will be notified once it\'s reviewed.');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to apply to campaign');
    } finally {
      setApplying(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading campaign details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading campaign</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <div className="mt-4">
                <button
                  onClick={handleGoBack}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Campaign not found</h3>
        <p className="text-gray-600 mb-4">The campaign you're looking for doesn't exist or has been removed.</p>
        <button
          onClick={handleGoBack}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  const canApply = campaign.status === 'active' &&
                   campaign.applicationStatus === 'not_applied' &&
                   !isDeadlinePassed(campaign.applicationDeadline);

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header with Back Button */}
      <div className="mb-6">
        <button
          onClick={handleGoBack}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors mb-4"
        >
          <IconArrowLeft className="w-5 h-5" />
          Back to Campaign List
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Campaign Details</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Campaign Header */}
          <div className="panel p-6">
            <div className="flex items-start gap-4 mb-4">
              {campaign.image && (
                <img
                  src={campaign.image}
                  alt={campaign.name}
                  className="w-16 h-16 object-contain rounded-lg border border-gray-200"
                />
              )}
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{campaign.name}</h2>
                <div className="flex flex-wrap gap-2 mb-3">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold border ${getStatusColor(campaign.status)}`}>
                    {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                  </span>
                  {campaign.applicationStatus && (
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold border ${getApplicationStatusColor(campaign.applicationStatus)}`}>
                      {campaign.applicationStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  )}
                </div>
                <p className="text-gray-600 leading-relaxed">{campaign.description}</p>
              </div>
            </div>
          </div>

          {/* Campaign Details */}
          <div className="panel p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Campaign Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <IconDollarSign className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">Budget</p>
                    <p className="font-semibold text-lg">{formatCurrency(campaign.budget)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <IconCalendar className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Campaign Duration</p>
                    <p className="font-semibold">{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <IconClock className="w-5 h-5 text-orange-600" />
                  <div>
                    <p className="text-sm text-gray-600">Application Deadline</p>
                    <p className={`font-semibold ${isDeadlinePassed(campaign.applicationDeadline) ? 'text-red-600' : 'text-gray-900'}`}>
                      {formatDate(campaign.applicationDeadline)}
                      {isDeadlinePassed(campaign.applicationDeadline) && (
                        <span className="text-red-600 text-sm ml-2">(Expired)</span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <IconUsers className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600">Applications</p>
                    <p className="font-semibold">{campaign.metrics.totalApplications} total, {campaign.metrics.approvedApplications} approved</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div className="panel p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Requirements</h3>
            <p className="text-gray-700 leading-relaxed">{campaign.requirements}</p>
          </div>

          {/* Target Audience */}
          <div className="panel p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Target Audience</h3>
            <p className="text-gray-700 leading-relaxed">{campaign.targetAudience}</p>
          </div>

          {/* Content Guidelines */}
          <div className="panel p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Content Guidelines</h3>
            <p className="text-gray-700 leading-relaxed mb-4">{campaign.contentGuidelines}</p>

            <h4 className="font-semibold text-gray-900 mb-3">Deliverables:</h4>
            <ul className="space-y-2">
              {campaign.deliverables.map((deliverable, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-green-600 mt-1">•</span>
                  <span className="text-gray-700">{deliverable}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Advertiser Info */}
          <div className="panel p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Advertiser</h3>
            <div className="flex items-center gap-3 mb-3">
              {campaign.advertiser.logo && (
                <img
                  src={campaign.advertiser.logo}
                  alt={campaign.advertiser.name}
                  className="w-12 h-12 object-contain rounded-lg border border-gray-200"
                />
              )}
              <div>
                <h4 className="font-semibold text-gray-900">{campaign.advertiser.name}</h4>
              </div>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed">{campaign.advertiser.description}</p>
          </div>

          {/* Application Status */}
          {campaign.applicationStatus && campaign.applicationStatus !== 'not_applied' && (
            <div className="panel p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Status</h3>
              <div className={`p-4 rounded-lg border ${getApplicationStatusColor(campaign.applicationStatus)}`}>
                <p className="font-semibold mb-2">
                  {campaign.applicationStatus.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </p>
                {campaign.applicationStatus === 'pending_sp_review' && (
                  <p className="text-sm">Your application is being reviewed by our team. You'll be notified once it's processed.</p>
                )}
                {campaign.applicationStatus === 'pending_advertiser_review' && (
                  <p className="text-sm">Your application passed initial review and is now being reviewed by the advertiser.</p>
                )}
                {campaign.applicationStatus === 'approved' && (
                  <p className="text-sm">Congratulations! Your application has been approved. You can now start creating content.</p>
                )}
                {campaign.applicationStatus === 'rejected' && (
                  <div>
                    <p className="text-sm mb-2">Your application was not approved.</p>
                    {campaign.rejectionReason && (
                      <p className="text-sm font-medium">Reason: {campaign.rejectionReason}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <div className="panel p-6">
            {canApply ? (
              <button
                onClick={handleApply}
                disabled={applying}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
              >
                {applying ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Applying...
                  </>
                ) : (
                  <>
                    <IconVideo className="w-5 h-5" />
                    Apply to Campaign
                  </>
                )}
              </button>
            ) : (
              <div className="text-center">
                {campaign.applicationStatus === 'not_applied' && isDeadlinePassed(campaign.applicationDeadline) && (
                  <p className="text-red-600 font-medium">Application deadline has passed</p>
                )}
                {campaign.status !== 'active' && (
                  <p className="text-gray-600 font-medium">Campaign is not currently active</p>
                )}
                {campaign.applicationStatus !== 'not_applied' && (
                  <p className="text-blue-600 font-medium">You have already applied to this campaign</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
